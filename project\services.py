# -*- coding: utf-8 -*-
"""
Project Detail Service
"""
from django.db import models
from django.db.models import Q
from django.conf import settings
from django.utils import timezone
from datetime import datetime, time
import logging

from .models import (
    TcdAppProject, TcdAppProjectSector, TcdAppProjectSkill, TcdAppProjectService,
    TcdAppProjectConsult, TcdAppMasConsultExp, TcdAppMasResult, TcdAppMasProjectCost,
    TcdAppMasProjectNumber, TcdAppMasProjectType, TcdAppMasConsultNumber,
    TcdAppMasConsultType, TcdAppMasConsultRating, TcdAppMasConsultCertificate
)
from search.models import TcdSector, TcdSkill, TcdService
from authentication.models import TcdAppMember, TcdUserConsult, TcdAppLogs
from authentication.utils import get_client_ip
from MCDC.models import TcdPersonalGeneralData, TcdCorporateGeneralData, TcdNoProfitGeneralData, TcdProjectSector, TcdProjectSkill, TcdProjectService
from utils.response import service_success_response, service_error_response
from search.notification_service import ProjectOwnerNotificationService

logger = logging.getLogger(__name__)


def convert_date_to_end_of_day(date_value):
    """
    Convert date to datetime with end of day (23:59:59) for proper comparison
    """
    if isinstance(date_value, str):
        date_value = datetime.strptime(date_value, '%Y-%m-%d').date()
    return datetime.combine(date_value, time(23, 59, 59))


def convert_date_to_start_of_day(date_value):
    """
    Convert date to datetime with start of day (00:00:00) for proper comparison
    """
    if isinstance(date_value, str):
        date_value = datetime.strptime(date_value, '%Y-%m-%d').date()
    return datetime.combine(date_value, time(0, 0, 0))


class ProjectViewService:
    """
    Service for managing project view counts
    """

    @staticmethod
    def get_project_view_count(project_id):
        """
        Get current view count for a project
        
        Args:
            project_id (int): Project ID
            
        Returns:
            dict: Service response with project view count data
        """
        try:
            project = TcdAppProject.objects.filter(id=project_id).first()
            
            if not project:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            return service_success_response(data={
                'project_id': project_id,
                'view_count': project.view
            })
            
        except Exception as e:
            logger.error(f"Error getting project view count: {str(e)}")
            return service_error_response(
                error_code=5000,
                message="System error"
            )

    @staticmethod
    def increment_project_view(project_id):
        """
        Increment project view count by 1
        
        Args:
            project_id (int): Project ID
            
        Returns:
            dict: Service response with old and new view counts
        """
        try:
            project = TcdAppProject.objects.filter(id=project_id).first()
            
            if not project:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            old_view_count = project.view
            
            # Increment view count
            updated_rows = TcdAppProject.objects.filter(id=project_id).update(
                view=models.F('view') + 1
            )
            
            if updated_rows == 0:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            return service_success_response(data={
                'project_id': project_id,
                'old_view_count': old_view_count,
                'new_view_count': old_view_count + 1,
                'updated': True
            })
            
        except Exception as e:
            logger.error(f"Error incrementing project view: {str(e)}")
            return service_error_response(
                error_code=5000,
                message="System error"
            )


class ProjectMatchingViewService:
    """
    Service for managing project matching view counts
    """

    @staticmethod
    def get_project_matching_view_count(app_project_id, user_consult_id):
        """
        Get current matching view count for a project-consultant pair
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            
        Returns:
            dict: Service response with matching view count data
        """
        try:
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            return service_success_response(data={
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'consult_view': project_consult.consult_view,
                'matching': project_consult.matching
            })
            
        except Exception as e:
            logger.error(f"Error getting project matching view count: {str(e)}")
            return service_error_response(
                error_code=5000,
                message="System error"
            )

    @staticmethod
    def increment_project_matching_view(app_project_id, user_consult_id):
        """
        Increment project matching view count by 1
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            
        Returns:
            dict: Service response with old and new view counts
        """
        try:
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            old_consult_view = project_consult.consult_view
            
            # Increment consult view count
            updated_rows = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).update(
                consult_view=models.F('consult_view') + 1
            )
            
            if updated_rows == 0:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            return service_success_response(data={
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'old_consult_view': old_consult_view,
                'new_consult_view': old_consult_view + 1,
                'matching': project_consult.matching,
                'updated': True
            })
            
        except Exception as e:
            logger.error(f"Error incrementing project matching view: {str(e)}")
            return service_error_response(
                error_code=5000,
                message="System error"
            )


class ConsultantMemberViewService:
    """
    Service for managing consultant view counts by members
    """

    @staticmethod
    def get_consultant_member_view_count(app_project_id, user_consult_id, app_member_id):
        """
        Get current member view count for a project-consultant-member combination
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            app_member_id (int): Member ID
            
        Returns:
            dict: Service response with member view count data
        """
        try:
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id,
                app_member_id=app_member_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            return service_success_response(data={
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'app_member_id': app_member_id,
                'member_view': project_consult.member_view,
                'matching': project_consult.matching
            })
            
        except Exception as e:
            logger.error(f"Error getting consultant member view count: {str(e)}")
            return service_error_response(
                error_code=5000,
                message="System error"
            )

    @staticmethod
    def increment_consultant_member_view(app_project_id, user_consult_id, app_member_id):
        """
        Increment consultant member view count by 1
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            app_member_id (int): Member ID who is viewing
            
        Returns:
            dict: Service response with old and new view counts
        """
        try:
            # First, check if the record exists
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id,
                app_member_id=app_member_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            old_member_view = project_consult.member_view
            
            # Increment member view count
            updated_rows = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id,
                app_member_id=app_member_id
            ).update(
                member_view=models.F('member_view') + 1
            )
            
            if updated_rows == 0:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            return service_success_response(data={
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'app_member_id': app_member_id,
                'old_member_view': old_member_view,
                'new_member_view': old_member_view + 1,
                'matching': project_consult.matching,
                'updated': True
            })
            
        except Exception as e:
            logger.error(f"Error incrementing consultant member view: {str(e)}")
            return service_error_response(
                error_code=5000,
                message="System error"
            )


class ProjectDetailService:
    """
    Service for retrieving detailed project information
    """

    @staticmethod
    def get_project_detail(project_id, user_consult_id=None, language='th'):
        """
        Get detailed project information including organization, sectors, skills, services, and matching data
        
        Args:
            project_id (int): Project ID
            user_consult_id (int, optional): Consultant user ID for matching data
            language (str): Language preference ('th' or 'en')
            
        Returns:
            dict: Service response with detailed project data
        """
        try:
            # Get project with related data
            project = TcdAppProject.objects.select_related(
                'app_member'
            ).filter(id=project_id).first()
            
            if not project:
                    return service_error_response(
                        error_code=3002,
                        message="Project not found"
                    )
                
                # Increment view count
            TcdAppProject.objects.filter(id=project_id).update(view=models.F('view') + 1)
            
            # Get organization data
            organization_data = ProjectDetailService._get_organization_data(project.app_member, language)
            
            # Get master data
            master_data = ProjectDetailService._get_master_data(project, language)
            
            # Get sectors and skills
            sectors_data = ProjectDetailService._get_sectors_and_skills(project_id)
            
            # Get services
            services_data = ProjectDetailService._get_services(project_id)
            
            # Build document download URL
            document_url = None
            if project.ref:
                base_file_url = getattr(settings, 'BASE_FILE_URL')
                matching_sub_dir = getattr(settings, 'MATCHING_SUB_DIR')
                document_url = base_file_url + matching_sub_dir + project.ref
            
            # Get matching data if consultant user ID provided
            matching_data = None
            contact_button_data = {'show': False}
            
            consultant_favorite_data = '0'
            if user_consult_id:
                matching_data = ProjectDetailService._get_matching_data(project_id, user_consult_id)
                contact_button_data = ProjectDetailService._get_contact_button_data(project_id, user_consult_id)
                project_consult = TcdAppProjectConsult.objects.filter(app_project_id=project_id, user_consult_id=user_consult_id).first()
                if project_consult:
                    consultant_favorite_data = project_consult.consult_favorite
            
            # Build response data
            response_data = {
                'project_name': project.name,
                'view_count': project.view + 1,  # Include the increment
                'purpose': project.purpost,
                'activity_scope': project.activity,
                'project_period_start': project.period_start,
                'project_period_end': project.period_end,
                'announcement_period_start': project.start_date,
                'announcement_period_end': project.end_date,
                'keyword': project.keyword or '',
                'document_url': document_url,  # ปุ่ม ดาวน์โหลดเอกสาร
                'organization': organization_data,
                **master_data,
                'sectors': sectors_data,
                'services': services_data,
                'contact_button': contact_button_data,
                'favorite_status': consultant_favorite_data,
                'matching_result': matching_data  # Always include matching_result field (null if no user_consult_id or no matching data)
            }
            
            return service_success_response(data=response_data)
                
        except Exception as e:
            logger.error(f"Error getting project detail: {str(e)}")
            return service_error_response(
                error_code=5000,
                message="System error"
            )
    
    @staticmethod
    def _build_organization_name(member, language='th'):
        """
        Build organization name with government sector details
        
        Args:
            member: TcdAppMember instance
            
        Returns:
            str: Complete organization name
        """
        try:
            organization_name = member.name or ''
            # logger.info(f'organization_name : {organization_name}')
            
            # Add department info
            if member.app_mas_department_id == 0 or member.app_mas_department_id is None:
                # Other department
                if member.app_mas_department_other:
                    organization_name += f" / {member.app_mas_department_other}"
                    # logger.info(f"organization_name: {organization_name}")
            else:
                # Get department name from foreign key
                try:
                    from authentication.models import TcdAppMasDepartment
                    department = TcdAppMasDepartment.objects.filter(id=member.app_mas_department_id).first()
                    if department:
                        if language == 'th':
                            dept_name = department.name_th
                        else:
                            dept_name = department.name_en
                        if dept_name:
                            logger.info(f"dept_name: {dept_name}")
                            organization_name += f" / {dept_name}"
                except:
                    pass
            
            # Add ministry info
            if member.app_mas_ministry_id == 0 or member.app_mas_ministry_id is None:
                # Other ministry
                if member.app_mas_ministry_other:
                    logger.info(f"member.app_mas_ministry_other: {member.app_mas_ministry_other}")
                    organization_name += f" / {member.app_mas_ministry_other}"
            else:
                # Get ministry name from foreign key
                try:
                    from authentication.models import TcdAppMasMinistry
                    ministry = TcdAppMasMinistry.objects.filter(id=member.app_mas_ministry_id).first()
                    if ministry:
                        if language == 'th':
                            ministry_name = ministry.name_th
                        else:
                            ministry_name = ministry.name_en
                        if ministry_name:
                            logger.info(f"ministry_name: {ministry_name}")
                            organization_name += f" / {ministry_name}"
                except:
                    pass
            
            # Add government sector info
            if member.app_mas_government_sector_id == 0 or member.app_mas_government_sector_id is None:
                # Other government sector
                if member.app_mas_government_sector_other:
                    logger.info(f"member.app_mas_government_sector_other: {member.app_mas_government_sector_other}")
                    organization_name += f" / {member.app_mas_government_sector_other}"
            else:
                # Get government sector name from foreign key
                try:
                    from authentication.models import TcdAppMasGovernmentSector
                    gov_sector = TcdAppMasGovernmentSector.objects.filter(id=member.app_mas_government_sector_id).first()
                    if gov_sector:
                        if language == 'th':
                            sector_name = gov_sector.name_th
                        else:
                            sector_name = gov_sector.name_en
                        if sector_name:
                            logger.info(f"sector_name: {sector_name}")
                            organization_name += f" / {sector_name}"
                except:
                    pass
            
            return organization_name
            
        except Exception as e:
            logger.error(f"Error building organization name: {str(e)}")
            return member.name or 'Unknown Organization'
    
    @staticmethod
    def _get_organization_data(app_member, language='th'):
        """
        Get organization data based on member type
        
        Args:
            app_member: TcdAppMember instance
            
        Returns:
            dict: Organization data
        """
        try:
            organization_data = {
                'name': '',
                'phone': '',
                'email': '',
                'type': '',
                'website': ''
            }
            
            # Get member type name from the foreign key relationship
            member_type_name = ''
            if app_member.app_mas_member_type:
                if language == 'th':
                    member_type_name = app_member.app_mas_member_type.name_th
                else:
                    member_type_name = app_member.app_mas_member_type.name_en
            
            # Use the fields directly from TcdAppMember model
            organization_data.update({
                'name': ProjectDetailService._build_organization_name(app_member, language),
                'phone': app_member.phone or '',
                'email': app_member.email or '',
                'type': member_type_name,
                'website': app_member.website or ''
            })
            
            return organization_data
            
        except Exception as e:
            logger.error(f"Error getting organization data: {str(e)}")
            return {
                'name': '',
                'phone': '',
                'email': '',
                'type': '',
                'website': ''
            }
    
    @staticmethod
    def _get_master_data(project: TcdAppProject, language='th'):
        """
        Get master data information for the project
        
        Args:
            project: TcdAppProject instance
            language (str): Language preference ('th' or 'en')
            
        Returns:
            dict: Master data information
        """
        master_data = {
            'consultant_experience': '',
            'matching_result_type': '',
            'contract_value': '',
            'project_count': '',
            'project_sector_count': '',
            'project_type': '',
            'consultant_count': '',
            'consultant_type': '',
            'consultant_level': '',
            'registration_certificate': ''
        }
        
        try:
            # Get consultant experience
            if project.consult_exp is not None:
                consult_exp = TcdAppMasConsultExp.objects.filter(id=project.consult_exp).first()
                if consult_exp:
                    master_data['consultant_experience'] = consult_exp.name_en if language == 'en' else consult_exp.name_th
            
            # Get matching result type
            if project.result is not None:
                result = TcdAppMasResult.objects.filter(id=project.result).first()
                if result:
                    master_data['matching_result_type'] = result.name_en if language == 'en' else result.name_th
            
            # Get contract value
            if project.project_cost is not None:
                project_cost = TcdAppMasProjectCost.objects.filter(id=project.project_cost).first()
                if project_cost:
                    master_data['contract_value'] = project_cost.name_en if language == 'en' else project_cost.name_th
            
            # Get project count
            if project.project_number is not None:
                project_number = TcdAppMasProjectNumber.objects.filter(id=project.project_number).first()
                if project_number:
                    master_data['project_count'] = project_number.name_en if language == 'en' else project_number.name_th
            
            # Get project type
            if project.project_type is not None:
                project_type = TcdAppMasProjectType.objects.filter(id=project.project_type).first()
                if project_type:
                    master_data['project_type'] = project_type.name_en if language == 'en' else project_type.name_th
            
            # Get consultant count
            if project.consult_number is not None:
                consult_number = TcdAppMasConsultNumber.objects.filter(id=project.consult_number).first()
                if consult_number:
                    master_data['consultant_count'] = consult_number.name_en if language == 'en' else consult_number.name_th
            
            # Get consultant type
            if project.consult_type is not None:
                consult_type = TcdAppMasConsultType.objects.filter(id=project.consult_type).first()
                if consult_type:
                    master_data['consultant_type'] = consult_type.name_en if language == 'en' else consult_type.name_th
            
            # Get consultant level
            if project.consult_rating is not None:
                consult_rating = TcdAppMasConsultRating.objects.filter(id=project.consult_rating).first()
                if consult_rating:
                    master_data['consultant_level'] = consult_rating.name_en if language == 'en' else consult_rating.name_th
            
            # Get registration certificate
            if project.consult_certificate is not None:
                consult_certificate = TcdAppMasConsultCertificate.objects.filter(id=project.consult_certificate).first()
                if consult_certificate:
                    master_data['registration_certificate'] = consult_certificate.name_en if language == 'en' else consult_certificate.name_th
            
            # Calculate project sector count
            if project.project_number is not None:
                project_number = TcdAppMasProjectNumber.objects.filter(id=project.project_number).first()
                if project_number:
                    master_data['project_sector_count'] = project_number.name_en if language == 'en' else project_number.name_th
            
        except Exception as e:
            logger.error(f"Error getting master data: {str(e)}")
        
        return master_data

    @staticmethod
    def _get_sectors_and_skills(project_id):
        """
        Get sectors and their associated skills for the project
        
        Args:
            project_id (int): Project ID
            
        Returns:
            list: List of sectors with their skills
        """
        try:
            sectors_data = []
            
            # Get project sectors
            project_sectors = TcdAppProjectSector.objects.filter(
                app_project_id=project_id
            ).values_list('sector_id', flat=True)
            
            for sector_id in project_sectors:
                sector = TcdSector.objects.filter(id=sector_id).first()
                if sector:
                    # Get skills for this sector in this project
                    project_skills = TcdAppProjectSkill.objects.filter(
                        app_project_id=project_id,
                        app_project_sector_id__in=TcdAppProjectSector.objects.filter(
                            app_project_id=project_id,
                            sector_id=sector_id
                        ).values_list('id', flat=True)
                    ).values_list('skill_id', flat=True)
                    
                    skills_data = []
                    for skill_id in project_skills:
                        skill = TcdSkill.objects.filter(id=skill_id).first()
                        if skill:
                            skills_data.append({
                                'skill_code': skill.code or '',
                                'skill_name_th': skill.name_th or '',
                                'skill_name_en': skill.name_en or '',
                                'skill_display': f"{skill.code or ''} : {skill.name_th or ''} : {skill.name_en or ''}".strip(' :')
                            })
                    
                    sectors_data.append({
                        'sector_code': sector.code or '',
                        'sector_name_th': sector.name_th or '',
                        'sector_name_en': sector.name_en or '',
                        'sector_display': f"{sector.code or ''} : {sector.name_th or ''} : {sector.name_en or ''}".strip(' :'),
                        'skills': skills_data
                    })
            
            return sectors_data
            
        except Exception as e:
            logger.error(f"Error getting sectors and skills: {str(e)}")
            return []

    @staticmethod
    def _get_services(project_id):
        """
        Get services for the project
        
        Args:
            project_id (int): Project ID
            
        Returns:
            list: List of services
        """
        try:
            services_data = []
            
            # Get project services
            project_services = TcdAppProjectService.objects.filter(app_project_id=project_id)
            
            for project_service in project_services:
                service = TcdService.objects.filter(id=project_service.service_id).first()
                if service:
                    services_data.append({
                        'service_code': service.code or '',
                        'service_name_th': service.name_th or '',
                        'service_name_en': service.name_en or '',
                        'service_display': f"{service.code or ''} : {service.name_th or ''} : {service.name_en or ''}".strip(' :')
                    })
            
            return services_data
            
        except Exception as e:
            logger.error(f"Error getting services: {str(e)}")
            return []

    @staticmethod
    def _get_matching_data(project_id, user_consult_id):
        """
        Get matching data for consultant
        
        Args:
            project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            
        Returns:
            float: Matching percentage or None
        """
        try:
            matching = TcdAppProjectConsult.objects.filter(
                app_project_id=project_id,
                user_consult_id=user_consult_id
            ).first()
            
            return matching.matching if matching else None
            
        except Exception as e:
            logger.error(f"Error getting matching data: {str(e)}")
            return None

    @staticmethod
    def _get_contact_button_data(project_id, user_consult_id):
        """
        Get contact button data for consultant
        
        Args:
            project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            
        Returns:
            dict: Contact button data
        """
        try:
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=project_id,
                user_consult_id=user_consult_id
            ).first()
            
            if project_consult:
                is_sent = project_consult.consult_send == 1
                return {
                    'show': True,
                    'text': 'ส่งข้อมูลติดต่อแล้ว' if is_sent else 'ส่งข้อมูลติดต่อ',
                    'is_sent': is_sent
                }
            
            return {'show': False}
            
        except Exception as e:
            logger.error(f"Error getting contact button data: {str(e)}")
            return {'show': False}


class ProjectInterestService:
    """
    Service for managing project interest status
    """

    @staticmethod
    def get_project_interest_status(app_project_id, user_consult_id):
        """
        Get current interest status for a project-consultant pair
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            
        Returns:
            dict: Service response with interest status data
        """
        try:
            project = TcdAppProject.objects.filter(id=app_project_id).first()
            if not project:
                return service_error_response(
                    error_code=3002
                )
            
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002
                )
            
            return service_success_response(data={
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'app_member_id': project_consult.app_member_id,
                'consult_send': project_consult.consult_send,
                'is_interested': project_consult.consult_send == 1
            })
            
        except Exception as e:
            logger.error(f"Error getting project interest status: {str(e)}")
            return service_error_response(
                error_code=5000
            )

    @staticmethod
    def update_project_interest_status(app_project_id, user_consult_id, request=None):
        """
        Toggle project interest status (0 -> 1, other -> 0)
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            
        Returns:
            dict: Service response with updated interest status
        """
        try:
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002
                )
            
            old_consult_send = project_consult.consult_send
            current_time = timezone.now()
            
            # Toggle logic: if 0 -> 1, else -> 0
            if old_consult_send == 0:
                new_consult_send = 1
                consult_send_date = current_time
                action = 'interested'
            else:
                new_consult_send = 0
                consult_send_date = None
                action = 'not_interested'
            
            # Update the record
            updated_rows = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).update(
                consult_send=new_consult_send,
                consult_send_date=consult_send_date,
                update_date=current_time
            )
            
            if updated_rows == 0:
                return service_error_response(
                    error_code=3002
                )
            
            # Log the action to TcdAppLogs
            action_log_text = "ยกเลิกยื่นความสนใจ"
            if old_consult_send == 0:
                action_log_text = "ยื่นความสนใจ"
            project = TcdAppProject.objects.filter(id=app_project_id).first()
            consultant = TcdUserConsult.objects.filter(id=user_consult_id).first()
            try:
                TcdAppLogs.objects.create(
                    action_date=timezone.now(),
                    action_log=action_log_text,
                    remark=f"{action_log_text} โครงการ {project.name if project else ''} สำเร็จ",
                    ip_address=get_client_ip(request) or "",
                    user_consult_id=user_consult_id,
                    name=ConsultantFavoriteService._get_consultant_display_name(consultant, user_consult_id),
                    type="APPCONSULTANT"
                )
            except Exception as e:
                logger.error(f"Error logging action to TcdAppLogs: {str(e)}")

            # Send notification to project owner when consultant shows interest
            notification_result = None
            member = TcdAppMember.objects.filter(id=project_consult.app_member_id).first()
            if action == 'interested' and member and member.is_notification == '1':  # Only send notification when consultant shows interest
                try:
                    logger.info(f"Sending notification to project owner for project {app_project_id}, consultant {user_consult_id}")
                    notification_result = ProjectOwnerNotificationService.notify_project_owner(
                        app_project_id=app_project_id,
                        user_consult_id=user_consult_id,
                        language='th'
                    )
                    logger.info(f"Notification result: {notification_result.get('success', False)}")
                except Exception as e:
                    logger.error(f"Error sending notification: {str(e)}")
                    # Don't fail the main operation if notification fails
                    notification_result = {'success': False, 'error': str(e)}
            
            response_data = {
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'app_member_id': project_consult.app_member_id,
                'old_consult_send': old_consult_send,
                'new_consult_send': new_consult_send,
                'is_interested': new_consult_send == 1,
                'action': action,
                'updated': True
            }
            
            # Add notification result if notification was sent
            if notification_result is not None:
                response_data['notification_sent'] = notification_result.get('success', False)
                if not notification_result.get('success', False):
                    response_data['notification_error'] = notification_result.get('error', 'Unknown error')
            
            return service_success_response(data=response_data)
            
        except Exception as e:
            logger.error(f"Error updating project interest status: {str(e)}")
            
            action_log_text = "ยกเลิกยื่นความสนใจ"
            if old_consult_send == 0:
                action_log_text = "ยื่นความสนใจ"
            project = TcdAppProject.objects.filter(id=app_project_id).first()
            consultant = TcdUserConsult.objects.filter(id=user_consult_id).first()
            TcdAppLogs.objects.create(
                action_date=timezone.now(),
                action_log=action_log_text,
                remark=f"{action_log_text} โครงการ {project.name if project else ''} ไม่สำเร็จ",
                ip_address=get_client_ip(request) or "",
                user_consult_id=user_consult_id,
                name=ConsultantFavoriteService._get_consultant_display_name(consultant, user_consult_id),
                type="APPCONSULTANT"
            )
            
            return service_error_response(
                error_code=5000
            )


class ProjectSearchService:
    """
    Service for project search functionality
    """

    @staticmethod
    def search_projects(filters, page=1, page_size=10, language='th'):
        """
        Search projects with comprehensive filtering and pagination
        
        Args:
            filters (dict): Search filters from serializer
            page (int): Page number for pagination
            page_size (int): Number of items per page
            
        Returns:
            dict: Service response with search results
        """
        try:
            # Build base queryset - only active projects (status='1') and within date range
            now = timezone.now()
            today = now.date().strftime('%Y-%m-%d')

            queryset = TcdAppProject.objects.filter(
                status='1',
                app_member__status='1'
            ).extra(
                where=["start_date <= %s AND end_date >= %s"],
                params=[today, today]
            )
            
            # Apply filters
            if filters.get('project_name'):
                queryset = queryset.filter(name__icontains=filters['project_name'])
            
            if filters.get('organization_name'):
                org_name_filter = filters['organization_name']
                queryset = queryset.filter(
                    Q(app_member__name__icontains=org_name_filter) |
                    Q(app_member__app_mas_government_sector__name_th__icontains=org_name_filter) |
                    Q(app_member__app_mas_government_sector__name_en__icontains=org_name_filter) |
                    Q(app_member__app_mas_government_sector_other__icontains=org_name_filter) |
                    Q(app_member__app_mas_ministry__name_th__icontains=org_name_filter) |
                    Q(app_member__app_mas_ministry__name_en__icontains=org_name_filter) |
                    Q(app_member__app_mas_ministry_other__icontains=org_name_filter) |
                    Q(app_member__app_mas_department__name_th__icontains=org_name_filter) |
                    Q(app_member__app_mas_department__name_en__icontains=org_name_filter) |
                    Q(app_member__app_mas_department_other__icontains=org_name_filter)
                )
            
            if filters.get('keyword'):
                queryset = queryset.filter(keyword__icontains=filters['keyword'])
            
            # Date filters
            if filters.get('project_period_start') and filters.get('project_period_start') != "":
                queryset = queryset.filter(period_start__gte=convert_date_to_start_of_day(filters['project_period_start']))

            if filters.get('project_period_end') and filters.get('project_period_end') != "":
                queryset = queryset.filter(period_end__lte=convert_date_to_end_of_day(filters['project_period_end']))

            if filters.get('announcement_start_date') and filters.get('announcement_start_date') != "":
                queryset = queryset.filter(start_date__gte=convert_date_to_start_of_day(filters['announcement_start_date']))

            if filters.get('announcement_end_date') and filters.get('announcement_end_date') != "":
                queryset = queryset.filter(end_date__lte=convert_date_to_end_of_day(filters['announcement_end_date']))
            
            # Sector filter
            if filters.get('sector_ids'):
                sector_ids_input = filters['sector_ids']
                if isinstance(sector_ids_input, list):
                    # Handle list input
                    sector_ids = [int(x) for x in sector_ids_input if str(x).strip()]
                elif isinstance(sector_ids_input, str):
                    # Handle string input
                    sector_ids = [int(x.strip()) for x in sector_ids_input.split(',') if x.strip()]
                else:
                    sector_ids = []
                
                if sector_ids:
                    project_ids_with_sectors = TcdAppProjectSector.objects.filter(
                        sector_id__in=sector_ids
                    ).values_list('app_project_id', flat=True)
                    queryset = queryset.filter(id__in=project_ids_with_sectors)
            
            # View count filter
            if filters.get('min_view_count') is not None:
                queryset = queryset.filter(view__gte=filters['min_view_count'])
            
            if filters.get('max_view_count') is not None:
                queryset = queryset.filter(view__lte=filters['max_view_count'])
            
            # Sorting
            sort_by = filters.get('sort_by', '-id')
            valid_sort_fields = [
                'name', '-name', 'create_date', '-create_date',
                'view', '-view', 'period_start', '-period_start',
                'period_end', '-period_end', 'start_date', '-start_date',
                'end_date', '-end_date', 'id', '-id'
            ]
            
            if sort_by in valid_sort_fields:
                queryset = queryset.order_by(sort_by)
            else:
                queryset = queryset.order_by('-id')
            
            # Get total count
            total_count = queryset.count()
            
            # Apply pagination
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            projects = queryset[start_index:end_index]
            
            # Build response data
            results = []
            for project in projects:
                # Get organization name
                member = TcdAppMember.objects.filter(id=project.app_member_id).first()
                organization_name = ConsultantSuitableProjectsService._build_organization_name(member, language)
                
                # Get sectors
                sectors = ProjectSearchService._get_project_sectors(project.id)
                
                project_data = {
                    'project_id': int(project.id),  # Force convert to int
                    'project_name': project.name or '',
                    'organization_name': organization_name,
                    'announcement_period_start': project.start_date,
                    'announcement_period_end': project.end_date,
                    'sectors': sectors,
                    'view_count': project.view or 0,
                    'matching_result': None  # Will be populated if user is authenticated
                }
                
                results.append(project_data)
            
            return service_success_response(data={
                'results': results,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': (total_count + page_size - 1) // page_size,
                    'has_next': end_index < total_count,
                    'has_previous': page > 1
                }
            })
            
        except Exception as e:
            logger.error(f"Error in project search: {str(e)}")
            return service_error_response(
                error_code=5000
            )
    
    @staticmethod
    def _get_project_sectors(project_id):
        """
        Get sectors for a project
        
        Args:
            project_id (int): Project ID
            
        Returns:
            str: Comma-separated sector names
        """
        try:
            project_sectors = TcdAppProjectSector.objects.filter(app_project_id=project_id)
            sector_codes = []
            sector_code_result = '-'
            
            for project_sector in project_sectors:
                sector = TcdSector.objects.filter(id=project_sector.sector_id).first()
                if sector:
                    sector_codes.append(sector.code)
            
            if len(sector_codes) > 0:
                sector_code_result = ', '.join(filter(None, sector_codes))
            
            return sector_code_result
            
        except Exception as e:
            logger.error(f"Error getting project sectors: {str(e)}")
            return ''


class ProjectCountService:
    """
    Service for project count functionality
    """

    @staticmethod
    def count_projects(filters):
        """
        Count projects with comprehensive filtering
        
        Args:
            filters (dict): Search filters from serializer
            
        Returns:
            dict: Service response with project count
        """
        try:
            # Build base queryset - only active projects (status='Y' or '1')
            queryset = TcdAppProject.objects.filter(status__in=['Y', '1'])
            
            # Apply filters (same logic as search)
            if filters.get('project_name'):
                queryset = queryset.filter(name__icontains=filters['project_name'])
            
            if filters.get('organization_name'):
                org_name_filter = filters['organization_name']
                queryset = queryset.filter(
                    Q(app_member__name__icontains=org_name_filter) |
                    Q(app_member__app_mas_government_sector__name_th__icontains=org_name_filter) |
                    Q(app_member__app_mas_government_sector__name_en__icontains=org_name_filter) |
                    Q(app_member__app_mas_government_sector_other__icontains=org_name_filter) |
                    Q(app_member__app_mas_ministry__name_th__icontains=org_name_filter) |
                    Q(app_member__app_mas_ministry__name_en__icontains=org_name_filter) |
                    Q(app_member__app_mas_ministry_other__icontains=org_name_filter) |
                    Q(app_member__app_mas_department__name_th__icontains=org_name_filter) |
                    Q(app_member__app_mas_department__name_en__icontains=org_name_filter) |
                    Q(app_member__app_mas_department_other__icontains=org_name_filter)
                )
            
            if filters.get('keyword'):
                queryset = queryset.filter(keyword__icontains=filters['keyword'])
            
            # Date filters
            if filters.get('project_period_start') and filters.get('project_period_start') != "":
                queryset = queryset.filter(period_start__gte=convert_date_to_start_of_day(filters['project_period_start']))

            if filters.get('project_period_end') and filters.get('project_period_end') != "":
                queryset = queryset.filter(period_end__lte=convert_date_to_end_of_day(filters['project_period_end']))

            if filters.get('announcement_period_start') and filters.get('announcement_period_start') != "":
                queryset = queryset.filter(start_date__gte=convert_date_to_start_of_day(filters['announcement_period_start']))

            if filters.get('announcement_period_end') and filters.get('announcement_period_end') != "":
                queryset = queryset.filter(end_date__lte=convert_date_to_end_of_day(filters['announcement_period_end']))
            
            # Sector filter
            if filters.get('sector_ids'):
                sector_ids_input = filters['sector_ids']
                if isinstance(sector_ids_input, list):
                    # Handle list input
                    sector_ids = [int(x) for x in sector_ids_input if str(x).strip()]
                elif isinstance(sector_ids_input, str):
                    # Handle string input
                    sector_ids = [int(x.strip()) for x in sector_ids_input.split(',') if x.strip()]
                else:
                    sector_ids = []
                
                if sector_ids:
                    project_ids_with_sectors = TcdAppProjectSector.objects.filter(
                        sector_id__in=sector_ids
                    ).values_list('app_project_id', flat=True)
                    queryset = queryset.filter(id__in=project_ids_with_sectors)
            
            # View count filter
            if filters.get('min_view_count') is not None:
                queryset = queryset.filter(view__gte=filters['min_view_count'])
            
            if filters.get('max_view_count') is not None:
                queryset = queryset.filter(view__lte=filters['max_view_count'])
            
            # Get count
            count = queryset.count()
            
            return service_success_response(data={
                'count': count,
                'filters_applied': filters
            })
            
        except Exception as e:
            logger.error(f"Error in project count: {str(e)}")
            return service_error_response(
                error_code=5000
            )
    

class MemberProjectSearchService:
    """
    Service for member project search functionality
    """

    @staticmethod
    def search_member_projects(app_member_id, filters, page=1, page_size=10, language='th'):
        """
        Search member's projects with filtering and sorting
        
        Args:
            app_member_id (int): Member ID
            filters (dict): Search filters from serializer
            page (int): Page number
            page_size (int): Items per page
            
        Returns:
            dict: Service response with member's projects
        """
        try:
            from django.db.models import Count, Q
            from datetime import datetime
            
            # Build base queryset - only completed projects for the member
            queryset = TcdAppProject.objects.filter(
                app_member_id=app_member_id,
                is_complete=True
            ).select_related('app_member')
            
            # Apply search filters
            if filters.get('project_name'):
                queryset = queryset.filter(name__icontains=filters['project_name'])
            
            # Date filters
            if filters.get('announcement_start_date') and filters.get('announcement_start_date') != "":
                queryset = queryset.filter(start_date__gte=convert_date_to_start_of_day(filters['announcement_start_date']))

            if filters.get('announcement_end_date') and filters.get('announcement_end_date') != "":
                queryset = queryset.filter(end_date__lte=convert_date_to_end_of_day(filters['announcement_end_date']))
            
            # Status filter
            if filters.get('status'):
                queryset = queryset.filter(status=filters['status'])
            
            # Apply sorting
            sort_by = filters.get('sort_by', 'most_viewed')
            
            if sort_by == 'latest':
                # โครงการล่าสุด - ORDER BY app_project.id DESC
                queryset = queryset.order_by('-id')
            elif sort_by == 'published':
                # โครงการที่ประกาศ - ORDER BY app_project.status DESC
                queryset = queryset.order_by('-status', '-id')
            else:  # most_viewed (default)
                # โครงการที่ดูมากที่สุด - ORDER BY app_project.view DESC
                queryset = queryset.order_by('-view', '-id')
            
            # Get total count
            total_count = queryset.count()
            
            # Apply pagination
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            projects = queryset[start_index:end_index]
            
            # Build response data
            results = []
            for project in projects:
                
                # Get status name
                status_name = MemberProjectSearchService._get_status_name(project.status, language)
                
                # Get matching count
                matching_count = MemberProjectSearchService._get_matching_count(project.id, project.result)
                
                # Get interested count
                interested_count = MemberProjectSearchService._get_interested_count(project.id)
                
                project_data = {
                    'project_id': int(project.id),  # Force convert to int
                    'project_name': project.name or '',
                    'announcement_start_date': project.start_date,
                    'announcement_end_date': project.end_date,
                    'status': int(project.status) if project.status.isdigit() else 0,
                    'status_name': status_name,
                    'matching_count': matching_count,
                    'interested_count': interested_count,
                    'view_count': project.view or 0
                }
                
                results.append(project_data)
            
            return service_success_response(data={
                'results': results,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': (total_count + page_size - 1) // page_size,
                    'has_next': end_index < total_count,
                    'has_previous': page > 1
                }
            })
            
        except Exception as e:
            logger.error(f"Error in member project search: {str(e)}")
            return service_error_response(
                error_code=5000
            )
    
    @staticmethod
    def _get_status_name(status, language='th'):
        """
        Get status name based on status value
        
        Args:
            status (str): Status value
            
        Returns:
            str: Status name in Thai
        """
        try:
            if status == '0':
                if language == 'th':
                    return 'ไม่เผยแพร่'
                else:
                    return 'Not publish'
            elif status == '1':
                if language == 'th':
                    return 'เผยแพร่'
                else:
                    return 'Publish'
            else:
                if language == 'th':
                    return 'ไม่ทราบสถานะ'
                else:
                    return 'Unknown Status'
        except:
            if language == 'th':
                return 'ไม่ทราบสถานะ'
            else:
                return 'Unknown Status'
    
    @staticmethod
    def _get_matching_count(project_id, result_threshold):
        """
        Get matching count for a project
        
        Args:
            project_id (int): Project ID
            result_threshold (int): Minimum matching score threshold
            
        Returns:
            int: Count of matching consultants
        """
        try:
            if result_threshold is None:
                result_threshold = 0
            
            count = TcdAppProjectConsult.objects.filter(
                app_project_id=project_id,
                matching__gte=result_threshold
            ).count()
            
            return count
            
        except Exception as e:
            logger.error(f"Error getting matching count for project {project_id}: {str(e)}")
            return 0
    
    @staticmethod
    def _get_interested_count(project_id):
        """
        Get interested count for a project
        
        Args:
            project_id (int): Project ID
            
        Returns:
            int: Count of interested consultants
        """
        try:
            count = TcdAppProjectConsult.objects.filter(
                app_project_id=project_id,
                consult_send=1
            ).count()
            
            return count
            
        except Exception as e:
            logger.error(f"Error getting interested count for project {project_id}: {str(e)}")
            return 0


class ProjectInterestedConsultantsService:
    """
    Service for getting interested consultants for a project
    """

    @staticmethod
    def get_interested_consultants(project_id, page=1, page_size=10, sort_by_matching=False):
        """
        Get list of consultants who are interested in a project (consult_send = 1)
        
        Args:
            project_id (int): Project ID
            page (int): Page number (default: 1)
            page_size (int): Number of items per page (default: 10)
            sort_by_matching (bool): Whether to sort by matching result (default: False)
            
        Returns:
            dict: Service response with interested consultants data and pagination
        """
        try:
            from django.core.paginator import Paginator
            from django.db.models import Q
            
            # Check if project exists
            project = TcdAppProject.objects.filter(id=project_id).first()
            if not project:
                return service_error_response(
                    error_code=3002
                )
            
            # Build base queryset for interested consultants
            queryset = TcdAppProjectConsult.objects.filter(
                app_project_id=project_id,
                consult_send=1
            )
            
            # Apply sorting based on sort_by_matching parameter
            if sort_by_matching:
                # Sort by matching DESC, rating ASC, register_no ASC
                queryset = queryset.order_by('-matching', 'rating', 'register_no')
            else:
                # Default sort by id DESC
                queryset = queryset.order_by('-id')
            
            # Setup pagination
            paginator = Paginator(queryset, page_size)
            total_count = paginator.count
            
            # Validate page number
            if page < 1:
                page = 1
            elif page > paginator.num_pages and total_count > 0:
                page = paginator.num_pages
            
            try:
                page_obj = paginator.page(page)
            except:
                # If page is out of range, return empty results
                return service_success_response(data={
                    'results': [],
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total_count': total_count,
                        'total_pages': paginator.num_pages,
                        'has_next': False,
                        'has_previous': False
                    },
                    'project_info': {
                        'project_id': project_id,
                        'project_name': project.name or '',
                        'total_interested': total_count
                    }
                })
            
            # Build results
            results = []
            for project_consult in page_obj:
                try:
                    consultant_data = ProjectInterestedConsultantsService._build_consultant_data(project_consult)
                    if consultant_data:
                        results.append(consultant_data)
                except Exception as e:
                    logger.warning(f"Error building consultant data for user_consult_id {project_consult.user_consult_id}: {str(e)}")
                    continue
            
            # Build pagination info
            pagination = {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': paginator.num_pages,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
            
            # Build project info
            project_info = {
                'project_id': project_id,
                'project_name': project.name or '',
                'total_interested': total_count
            }
            
            return service_success_response(data={
                'results': results,
                'pagination': pagination,
                'project_info': project_info
            })
            
        except Exception as e:
            logger.error(f"Error getting interested consultants: {str(e)}")
            return service_error_response(
                error_code=5000
            )
    
    @staticmethod
    def _build_consultant_data(project_consult):
        """
        Build consultant data from project_consult record
        
        Args:
            project_consult: TcdAppProjectConsult instance
            
        Returns:
            dict: Consultant data
        """
        try:
            # Get consultant data by user_consult_id
            consultant = TcdUserConsult.objects.filter(id=project_consult.user_consult_id).first()
            if not consultant:
                return None
            
            # Get consultant name and details
            consultant_name = ProjectInterestedConsultantsService._get_consultant_name(consultant.id)
            consultant_details = ProjectInterestedConsultantsService._get_consultant_details(consultant)
            
            return {
                'id': project_consult.id,
                'consultant_id': consultant.id,
                'consultant_name': consultant_name,
                'consultant_details': consultant_details,
                'matching_result': float(project_consult.matching) if project_consult.matching else 0.0,
                'register_no': int(project_consult.register_no) if project_consult.register_no else 0,
                'rating': int(project_consult.rating) if project_consult.rating else 0,
                'consult_send_date': project_consult.consult_send_date.isoformat() if project_consult.consult_send_date else None,
                'member_view': project_consult.member_view or 0,
                'consult_view': project_consult.consult_view or 0,
                'member_favorite': project_consult.member_favorite == '1',
                'consult_favorite': project_consult.consult_favorite == '1'
            }
            
        except Exception as e:
            logger.error(f"Error building consultant data: {str(e)}")
            return None
    
    @staticmethod
    def _get_consultant_name(user_consult_id):
        """
        Get consultant name from various sources
        
        Args:
            user_consult_id (int): Consultant user ID
            
        Returns:
            str: Consultant name
        """
        try:
            # Try to get from personal data first
            personal_data = TcdPersonalGeneralData.objects.filter(user_consult_id=user_consult_id).first()
            if personal_data:
                name_parts = []
                if personal_data.first_name:
                    name_parts.append(personal_data.first_name)
                if personal_data.last_name:
                    name_parts.append(personal_data.last_name)
                if name_parts:
                    return ' '.join(name_parts)
            
            # Try corporate data
            corporate_data = TcdCorporateGeneralData.objects.filter(user_consult_id=user_consult_id).first()
            if corporate_data and corporate_data.name:
                return corporate_data.name
            
            # Try non-profit data
            nonprofit_data = TcdNoProfitGeneralData.objects.filter(user_consult_id=user_consult_id).first()
            if nonprofit_data and nonprofit_data.name:
                return nonprofit_data.name
            
            return f"ที่ปรึกษา ID: {user_consult_id}"
            
        except Exception as e:
            logger.error(f"Error getting consultant name: {str(e)}")
            return f"ที่ปรึกษา ID: {user_consult_id}"
    
    @staticmethod
    def _get_consultant_details(consultant):
        """
        Get consultant details
        
        Args:
            consultant: TcdUserConsult instance
            
        Returns:
            dict: Consultant details
        """
        try:
            # Get consult type display
            consult_type_display = "ไม่ระบุ"
            if consultant.consult_type == 1:
                consult_type_display = "อิสระ"
            elif consultant.consult_type == 2:
                consult_type_display = "นิติบุคคล"
            
            # Get corporate type display if applicable
            corporate_type_display = None
            if consultant.consult_type == 2 and consultant.corporate_type_id:
                try:
                    from MCDC.models import TcdCorporateType
                    corporate_type = TcdCorporateType.objects.filter(id=consultant.corporate_type_id).first()
                    if corporate_type:
                        corporate_type_display = corporate_type.name_th
                except:
                    pass
            
            # Get verify status display
            verify_display = "ไม่ระบุ"
            if consultant.verify == '1':
                verify_display = "ยืนยันแล้ว"
            elif consultant.verify == '0':
                verify_display = "ยังไม่ยืนยัน"
            
            return {
                'consult_type': consultant.consult_type or 0,
                'consult_type_display': consult_type_display,
                'corporate_type_id': int(consultant.corporate_type_id) if consultant.corporate_type_id else None,
                'corporate_type_display': corporate_type_display,
                'username': consultant.username or '',
                'email': consultant.email or '',
                'phone': consultant.phone or '',
                'verify': consultant.verify or '0',
                'verify_display': verify_display,
                'score': float(consultant.score) if consultant.score else 0.0,
                'is_active_matching': consultant.is_active_matching,
                'lang': consultant.lang or 'th'
            }
            
        except Exception as e:
            logger.error(f"Error getting consultant details: {str(e)}")
            return {
                'consult_type': 0,
                'consult_type_display': "ไม่ระบุ",
                'corporate_type_id': None,
                'corporate_type_display': None,
                'username': '',
                'email': '',
                'phone': '',
                'verify': '0',
                'verify_display': "ไม่ระบุ",
                'score': 0.0,
                'is_active_matching': False,
                'lang': 'th'
            }


class SuitableConsultantService:
    """
    Service for retrieving suitable consultants for a project
    """

    @staticmethod
    def get_suitable_consultants(project_id, consultant_type_filter_1=None, consultant_type_filter_2=None, sort_by_matching=False, sort_by_latest=False, page=1, page_size=10):
        """
        Get suitable consultants for a project with filtering and sorting
        
        Args:
            project_id (int): Project ID
            consultant_type_filter_1 (int, optional): Consultant type filter (1: Independent, 2: Corporate)
            consultant_type_filter_2 (str, optional): Corporate type ID filter
            sort_by_matching (bool): Whether to sort by matching score (default: False)
            sort_by_latest (bool): Whether to sort by latest consultants (default: False)
            page (int): Page number for pagination
            page_size (int): Number of items per page
            
        Returns:
            dict: Service response with suitable consultants data
        """
        try:
            # Get project details
            project = TcdAppProject.objects.filter(id=project_id).first()
            if not project:
                return service_error_response(
                    error_code=3002
                )
            
            # Build base query
            from django.db.models import Q
            
            # Base query: app_project_consult filtered by project_id and matching threshold
            queryset = TcdAppProjectConsult.objects.filter(
                app_project_id=project_id,
                matching__gte=project.result  # matching >= project.result
            )
            
            # Apply consultant type filter 1
            if consultant_type_filter_1 in [1, 2]:
                # Get user_consult_ids that match the consultant type
                matching_consultant_ids = TcdUserConsult.objects.filter(
                    consult_type=consultant_type_filter_1
                ).values_list('id', flat=True)
                queryset = queryset.filter(user_consult_id__in=matching_consultant_ids)
            
            # Apply consultant type filter 2 (corporate_type_id)
            if consultant_type_filter_2 and consultant_type_filter_2 != "":
                try:
                    corporate_type_id = int(consultant_type_filter_2)
                    # Get user_consult_ids that match the corporate type
                    matching_consultant_ids = TcdUserConsult.objects.filter(
                        corporate_type_id=corporate_type_id
                    ).values_list('id', flat=True)
                    queryset = queryset.filter(user_consult_id__in=matching_consultant_ids)
                except (ValueError, TypeError):
                    pass
            
            # Apply sorting
            if sort_by_matching:
                # Sort by matching DESC, rating ASC, register_no ASC
                queryset = queryset.order_by('-matching', 'rating', 'register_no')
            elif sort_by_latest:
                # Sort by latest consultants (based on TcdUserConsult creation/update)
                # We'll use the user_consult_id DESC as a proxy for latest
                queryset = queryset.order_by('-user_consult_id', '-matching')
            else:
                # Default sort by id DESC
                queryset = queryset.order_by('-id')
            
            # Get total count
            total_count = queryset.count()
            
            # Apply pagination
            offset = (page - 1) * page_size
            paginated_consultants = queryset[offset:offset + page_size]
            
            # Build consultant data
            consultants = []
            for project_consult in paginated_consultants:
                try:
                    # Get consultant object
                    consultant = TcdUserConsult.objects.filter(id=project_consult.user_consult_id).first()
                    if not consultant:
                        continue
                    
                    # Get consultant details using existing service
                    from authentication.services import ConsultantAuthService
                    consultant_detail = ConsultantAuthService.get_consultant_profile(consultant)
                    consultant_detail['src'] = None
                    
                    # Get consultant name from TcdUserConsultTeam if available
                    consultant_name = SuitableConsultantService._get_consultant_name(project_consult.user_consult_id)
                    
                    # Get consultant status (this would be from the UserConsult Detail API)
                    consultant_status = SuitableConsultantService._get_consultant_status(consultant)
                    
                    consultant_data = {
                        'consultant_id': int(project_consult.user_consult_id),
                        'consultant_name': consultant_name,
                        'consultant_status': consultant_status,
                        'favorite_status': int(project_consult.member_favorite) if project_consult.member_favorite else 0,  # 1: liked, 0: not liked
                        'matching_result': float(project_consult.matching),
                        'register_no': int(project_consult.register_no) if project_consult.register_no else 0,
                        'rating': int(project_consult.rating) if project_consult.rating else 0,
                        'consultant_detail': consultant_detail
                    }
                    
                    consultants.append(consultant_data)
                    
                except Exception as e:
                    logger.error(f"Error processing consultant {project_consult.user_consult_id}: {str(e)}")
                    continue
            
            # Calculate pagination info
            has_next = offset + page_size < total_count
            
            # Create response with pagination fields at root level
            # This is a special case where pagination fields need to be at root level
            return {
                'success': True,
                'data': {
                    'results': consultants
                },
                'page': page,
                'per_page': page_size,
                'total': total_count,
                'has_next': has_next
            }
            
        except Exception as e:
            logger.error(f"Error getting suitable consultants: {str(e)}")
            return service_error_response(
                error_code=5000
            )
    
    @staticmethod
    def _get_consultant_name(user_consult_id):
        """
        Get consultant name from TcdUserConsultTeam
        
        Args:
            user_consult_id (int): Consultant user ID
            
        Returns:
            str: Consultant full name or username
        """
        try:
            from authentication.models import TcdUserConsultTeam, TcdUserConsult
            
            # Try to get name from consultant team (admin user)
            consultant_team = TcdUserConsultTeam.objects.filter(
                user_consult_id=user_consult_id,
                is_admin=True
            ).first()
            
            if consultant_team and consultant_team.first_name and consultant_team.last_name:
                return f"{consultant_team.first_name} {consultant_team.last_name}".strip()
            
            # Fallback to username from TcdUserConsult
            consultant = TcdUserConsult.objects.filter(id=user_consult_id).first()
            if consultant:
                return consultant.username
            
            return "Unknown Consultant"
            
        except Exception as e:
            logger.error(f"Error getting consultant name for {user_consult_id}: {str(e)}")
            return "Unknown Consultant"
    
    @staticmethod
    def _get_consultant_status(consultant):
        """
        Get consultant status information
        This simulates calling the UserConsult Detail API
        
        Args:
            consultant: TcdUserConsult object
            
        Returns:
            dict: Consultant status information
        """
        try:
            # This would normally call the UserConsult Detail API
            # For now, we'll return basic status information from the consultant object
            
            from authentication.constants import ConsultantVerificationStatus, ConsultantType
            
            status_info = {
                'verify_status': consultant.verify if consultant.verify else '0',
                'verify_status_display': dict(ConsultantVerificationStatus.CHOICES).get(consultant.verify, 'ไม่ระบุ'),
                'consult_type': consultant.consult_type if consultant.consult_type else 0,
                'consult_type_display': dict(ConsultantType.CHOICES).get(consultant.consult_type, 'ไม่ระบุ'),
                'is_active_matching': consultant.is_active_matching if consultant.is_active_matching is not None else False,
                'score': float(consultant.score) if consultant.score else 0.0,
                'phone': consultant.phone if consultant.phone else '',
                'email': consultant.email if consultant.email else ''
            }
            
            return status_info
            
        except Exception as e:
            logger.error(f"Error getting consultant status: {str(e)}")
            return {
                'verify_status': '0',
                'verify_status_display': 'ไม่ระบุ',
                'consult_type': 0,
                'consult_type_display': 'ไม่ระบุ',
                'is_active_matching': False,
                'score': 0.0,
                'phone': '',
                'email': ''
            }


class ProjectFavoriteConsultantsService:
    """
    Service for getting favorite consultants for a project
    """

    @staticmethod
    def get_favorite_consultants(project_id, page=1, page_size=10, sort_by_matching=False):
        """
        Get list of favorite consultants for a project
        
        Args:
            project_id (int): Project ID
            page (int): Page number for pagination
            page_size (int): Number of items per page
            sort_by_matching (bool): Whether to sort by matching score
            
        Returns:
            dict: Service response with favorite consultants data
        """
        try:
            # Check if project exists
            project = TcdAppProject.objects.filter(id=project_id).first()
            if not project:
                return service_error_response(
                    error_code=3002
                )
            
            # Build base queryset for favorite consultants
            queryset = TcdAppProjectConsult.objects.filter(
                app_project_id=project_id,
                member_favorite='1'
            )
            
            # Apply sorting
            if sort_by_matching:
                # Sort by matching DESC, rating ASC, register_no ASC
                queryset = queryset.order_by('-matching', 'rating', 'register_no')
            else:
                # Default sort by id DESC
                queryset = queryset.order_by('-id')
            
            # Calculate pagination
            total_count = queryset.count()
            offset = (page - 1) * page_size
            has_next = offset + page_size < total_count
            
            # Get paginated results
            consultants = queryset[offset:offset + page_size]
            
            # Build response data
            results = []
            for consultant in consultants:
                consultant_data = ProjectFavoriteConsultantsService._build_consultant_data(consultant)
                results.append(consultant_data)
            
            # Get project info
            project_info = {
                'project_id': int(project.id),  # Force convert to int
                'project_name': project.name,
                'organization_name': project.app_member.name if project.app_member else ''
            }
            
            return service_success_response(data={
                'results': results,
                'project_info': project_info,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'has_next': has_next
                }
            })
            
        except Exception as e:
            logger.error(f"Error getting favorite consultants: {str(e)}")
            return service_error_response(
                error_code=5000
            )

    @staticmethod
    def _build_consultant_data(project_consult):
        """
        Build consultant data from project_consult record
        
        Args:
            project_consult: TcdAppProjectConsult instance
            
        Returns:
            dict: Consultant data
        """
        try:
            consultant_name = ProjectFavoriteConsultantsService._get_consultant_name(project_consult.user_consult_id)
            
            # Get consultant object for details
            consultant = TcdUserConsult.objects.filter(id=project_consult.user_consult_id).first()
            consultant_details = ProjectFavoriteConsultantsService._get_consultant_details(consultant)
            
            # Format consult_send_date
            consult_send_date_str = None
            if project_consult.consult_send_date:
                consult_send_date_str = project_consult.consult_send_date.strftime('%d %b %Y')
            
            return {
                'id': project_consult.id,
                'consultant_id': project_consult.user_consult_id,
                'consultant_name': consultant_name,
                'consultant_details': consultant_details,
                'matching_result': float(project_consult.matching),
                'register_no': int(project_consult.register_no),
                'rating': int(project_consult.rating),
                'consult_send_date': consult_send_date_str,
                'member_view': project_consult.member_view,
                'consult_view': project_consult.consult_view,
                'member_favorite': project_consult.member_favorite == '1',
                'consult_favorite': project_consult.consult_favorite == '1'
            }
            
        except Exception as e:
            logger.error(f"Error building consultant data: {str(e)}")
            return {
                'id': project_consult.id if project_consult else 0,
                'consultant_id': project_consult.user_consult_id if project_consult else 0,
                'consultant_name': 'Unknown',
                'consultant_details': {},
                'matching_result': 0.0,
                'register_no': 0,
                'rating': 0,
                'consult_send_date': None,
                'member_view': 0,
                'consult_view': 0,
                'member_favorite': False,
                'consult_favorite': False
            }

    @staticmethod
    def _get_consultant_name(user_consult_id):
        """
        Get consultant name from user_consult_id
        
        Args:
            user_consult_id (int): Consultant user ID
            
        Returns:
            str: Consultant name
        """
        try:
            consultant = TcdUserConsult.objects.filter(id=user_consult_id).first()
            if not consultant:
                return 'Unknown Consultant'
            
            # Get name based on consultant type
            if consultant.consult_type == 1:  # Individual
                # Get from personal data
                personal_data = TcdPersonalGeneralData.objects.filter(user_consult_id=user_consult_id).first()
                if personal_data:
                    # Get title name from the title foreign key
                    title_name = personal_data.title.name_th if personal_data.title else ''
                    return f"{title_name} {personal_data.first_name} {personal_data.last_name}".strip()
            else:  # Corporate
                # Get from corporate data
                corporate_data = TcdCorporateGeneralData.objects.filter(user_consult_id=user_consult_id).first()
                if corporate_data:
                    return corporate_data.name or corporate_data.name_en or 'Corporate Consultant'
                
                # Try non-profit data
                nonprofit_data = TcdNoProfitGeneralData.objects.filter(user_consult_id=user_consult_id).first()
                if nonprofit_data:
                    return nonprofit_data.name or nonprofit_data.name_en or 'Non-Profit Consultant'
            
            return consultant.username or 'Unknown Consultant'
            
        except Exception as e:
            logger.error(f"Error getting consultant name: {str(e)}")
            return 'Unknown Consultant'

    @staticmethod
    def _get_consultant_details(consultant):
        """
        Get detailed consultant information
        
        Args:
            consultant: TcdUserConsult instance
            
        Returns:
            dict: Consultant details
        """
        try:
            if not consultant:
                return {}
            
            # Get corporate type display
            corporate_type_display = None
            corporate_type_id = None
            
            if consultant.consult_type == 2:  # Corporate
                from MCDC.models import TcdCorporateType
                if consultant.corporate_type_id:
                    corporate_type_id = int(consultant.corporate_type_id)
                    corporate_type = TcdCorporateType.objects.filter(id=corporate_type_id).first()
                    if corporate_type:
                        corporate_type_display = corporate_type.name_th
            
            return {
                'consult_type': consultant.consult_type,
                'consult_type_display': 'บุคคลธรรมดา' if consultant.consult_type == 1 else 'นิติบุคคล',
                'corporate_type_id': corporate_type_id,
                'corporate_type_display': corporate_type_display,
                'username': consultant.username or '',
                'email': consultant.email or '',
                'phone': consultant.phone or '',
                'verify': consultant.verify or '',
                'verify_display': ProjectFavoriteConsultantsService._get_verify_display(consultant.verify),
                'score': float(consultant.score) if consultant.score else 0.0,
                'is_active_matching': consultant.is_active_matching == 'Y',
                'lang': consultant.lang or 'th'
            }
            
        except Exception as e:
            logger.error(f"Error getting consultant details: {str(e)}")
            return {}

    @staticmethod
    def _get_verify_display(verify_status):
        """
        Get display text for verify status
        
        Args:
            verify_status (str): Verify status code
            
        Returns:
            str: Display text
        """
        verify_map = {
            'Y': 'ยืนยันแล้ว',
            '1': 'ยืนยันแล้ว',
            'N': 'ยังไม่ยืนยัน',
            '0': 'ยังไม่ยืนยัน',
            'P': 'รอการยืนยัน',
            'R': 'ถูกปฏิเสธ'
        }
        return verify_map.get(verify_status, 'ไม่ทราบสถานะ')


class ProjectMemberFavoriteService:
    """
    Service for managing member favorite status for consultants
    """

    @staticmethod
    def get_member_favorite_status(app_project_id, user_consult_id, app_member_id):
        """
        Get current member favorite status for a project-consultant-member combination
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            app_member_id (int): Member ID who is logged in
            
        Returns:
            dict: Service response with member favorite status data
        """
        try:
            # Find the record in app_project_consult
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id,
                app_member_id=app_member_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            return service_success_response(data={
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'app_member_id': app_member_id,
                'member_favorite': project_consult.member_favorite,
                'is_favorite': project_consult.member_favorite == '1'
            })
            
        except Exception as e:
            logger.error(f"Error getting member favorite status: {str(e)}")
            return service_error_response(
                error_code=5000,
                message="System error"
            )
    
    @staticmethod
    def update_member_favorite_status(app_project_id, user_consult_id, app_member_id, request=None):
        """
        Toggle member favorite status (0 -> 1, other -> 0)
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID
            app_member_id (int): Member ID who is logged in
            request: Django request object for IP address
            
        Returns:
            dict: Service response with old and new favorite status
        """
        try:
            # First, check if the record exists
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id,
                app_member_id=app_member_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            old_member_favorite = project_consult.member_favorite
            
            # Toggle favorite status: if "0" -> "1", else -> "0"
            new_member_favorite = "1" if old_member_favorite == "0" else "0"
            
            # Determine action log message
            action_log = "เพิ่มรายการที่ชอบ" if old_member_favorite == "0" else "ยกเลิกรายการที่ชอบ"
            
            # Update the record
            updated_rows = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id,
                app_member_id=app_member_id
            ).update(
                member_favorite=new_member_favorite,
                update_date=timezone.now()
            )
            
            # Determine if update was successful
            update_success = updated_rows > 0
            remark = f"{action_log} สำเร็จ" if update_success else f"{action_log} ไม่สำเร็จ"
            
            # Log the action to TcdAppLogs
            try:
                # Get member information
                member = TcdAppMember.objects.filter(id=app_member_id).first()
                member_name = ""
                if member:
                    first_name = getattr(member, 'first_name', '') or ''
                    last_name = getattr(member, 'last_name', '') or ''
                    member_name = f"{first_name} {last_name}".strip()
                
                # Get IP address
                client_ip = ""
                if request:
                    client_ip = get_client_ip(request)
                
                # Create app logs record
                app_logs = TcdAppLogs(
                    action_date=timezone.now(),
                    action_log=action_log,
                    remark=remark,
                    ip_address=client_ip or "",
                    app_member_id=app_member_id,
                    user_consult_id=None,  # For member actions, this is None
                    name=member_name,
                    type="APPMEMBER"
                )
                
                # Save the log
                app_logs.save()
                logger.info(f"App logs saved successfully: action={action_log}, member_id={app_member_id}")
                
            except Exception as log_error:
                logger.error(f"Error saving app logs: {str(log_error)}")
                # Don't fail the main operation if logging fails
            
            if not update_success:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            # Determine action taken
            action = "favorited" if new_member_favorite == "1" else "unfavorited"
            
            return service_success_response(data={
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'app_member_id': app_member_id,
                'old_member_favorite': old_member_favorite,
                'new_member_favorite': new_member_favorite,
                'is_favorite': new_member_favorite == '1',
                'action': action,
                'updated': True
            })
            
        except Exception as e:
            logger.error(f"Error updating member favorite status: {str(e)}")
            
            # Log the failed action
            try:
                # Get member information
                member = TcdAppMember.objects.filter(id=app_member_id).first()
                member_name = ""
                if member:
                    first_name = getattr(member, 'first_name', '') or ''
                    last_name = getattr(member, 'last_name', '') or ''
                    member_name = f"{first_name} {last_name}".strip()
                
                # Get IP address
                client_ip = ""
                if request:
                    client_ip = get_client_ip(request)
                
                # Try to get old member favorite status for logging
                try:
                    project_consult = TcdAppProjectConsult.objects.filter(
                        app_project_id=app_project_id,
                        user_consult_id=user_consult_id,
                        app_member_id=app_member_id
                    ).first()
                    old_favorite = project_consult.member_favorite if project_consult else "0"
                except:
                    old_favorite = "0"  # Default fallback
                
                # Determine action log message for failed attempt
                action_log = "เพิ่มรายการที่ชอบ" if old_favorite == "0" else "ยกเลิกรายการที่ชอบ"
                remark = f"{action_log} ไม่สำเร็จ"
                
                # Create app logs record for failed attempt
                app_logs = TcdAppLogs(
                    action_date=timezone.now(),
                    action_log=action_log,
                    remark=remark,
                    ip_address=client_ip or "",
                    app_member_id=app_member_id,
                    user_consult_id=None,
                    name=member_name,
                    type="APPMEMBER"
                )
                
                # Save the log
                app_logs.save()
                logger.info(f"Failed action logged: action={action_log}, member_id={app_member_id}")
                
            except Exception as log_error:
                logger.error(f"Error saving failed action log: {str(log_error)}")
            
            return service_error_response(
                error_code=5000,
                message="System error"
            )


class ConsultantExperienceService:
    """
    Service for getting consultant experience projects
    """

    @staticmethod
    def get_consultant_experience(project_id=None, general_data_id=None, user_consult_id=None, consult_type=None, corporate_type_id=None, page=1, page_size=10, language='th'):
        """
        Get consultant experience projects filtered by project's sectors, skills, and services

        Args:
            project_id (int): Target project ID to match sectors/skills/services
            general_data_id (int): Consultant general data ID
            user_consult_id (int): Consultant user ID
            consult_type (int): Consultant type (1=individual, 2=corporate)
            corporate_type_id (int): Corporate type ID (1=corporate, other=non-profit)
            page (int): Page number (default: 1)
            page_size (int): Number of items per page (default: 10)
            language (str): Language preference ('th' or 'en')

        Returns:
            dict: Service response with consultant experience data and pagination
        """
        try:
            from django.core.paginator import Paginator
            from django.db.models import Q
            from MCDC.models import TcdProject

            # Step 1: Get sectors, skills, and services from the target project
            target_sectors = []
            target_skills = []
            target_services = []

            if project_id:
                # Get sectors from app_project_sector
                target_sectors = list(TcdAppProjectSector.objects.filter(
                    app_project_id=project_id
                ).values_list('sector_id', flat=True))

                # Get skills from app_project_skill
                target_skills = list(TcdAppProjectSkill.objects.filter(
                    app_project_id=project_id
                ).values_list('skill_id', flat=True))

                # Get services from app_project_service
                target_services = list(TcdAppProjectService.objects.filter(
                    app_project_id=project_id
                ).values_list('service_id', flat=True))

            # Step 2: Determine consultant type field
            general_data_field = ConsultantExperienceService._get_consultant_general_data(user_consult_id, consult_type, corporate_type_id)
            if not general_data_field:
                return service_error_response(error_code=4004)

            # Step 3: Build base query for consultant experience projects
            base_query = Q(is_approve=1) & Q(is_active=1) & Q(**{general_data_field: general_data_id})
            if project_id:
                base_query &= Q(id=project_id)

            # Step 4: Add filtering based on target project's sectors, skills, and services
            project_ids_with_matching_sectors = set()
            project_ids_with_matching_skills = set()
            project_ids_with_matching_services = set()

            # Filter by sectors if target sectors exist
            if target_sectors:
                project_ids_with_matching_sectors = set(
                    TcdProjectSector.objects.filter(
                        sector_id__in=target_sectors
                    ).values_list('project_id', flat=True)
                )

            # Filter by skills if target skills exist
            if target_skills:
                project_ids_with_matching_skills = set(
                    TcdProjectSkill.objects.filter(
                        skill_id__in=target_skills
                    ).values_list('project_id', flat=True)
                )

            # Filter by services if target services exist
            if target_services:
                project_ids_with_matching_services = set(
                    TcdProjectService.objects.filter(
                        service_id__in=target_services
                    ).values_list('project_id', flat=True)
                )

            # Combine filtering logic - projects must match at least one category that exists
            matching_project_ids = set()
            filter_applied = False

            if target_sectors:
                matching_project_ids.update(project_ids_with_matching_sectors)
                filter_applied = True

            if target_skills:
                if filter_applied:
                    # Intersect with existing matches (AND logic)
                    matching_project_ids = matching_project_ids.intersection(project_ids_with_matching_skills)
                else:
                    matching_project_ids.update(project_ids_with_matching_skills)
                    filter_applied = True

            if target_services:
                if filter_applied:
                    # Intersect with existing matches (AND logic)
                    matching_project_ids = matching_project_ids.intersection(project_ids_with_matching_services)
                else:
                    matching_project_ids.update(project_ids_with_matching_services)
                    filter_applied = True

            # Apply the filtering if any target criteria exist
            if filter_applied and matching_project_ids:
                base_query &= Q(id__in=matching_project_ids)
            elif filter_applied and not matching_project_ids:
                # No matching projects found, return empty result
                base_query &= Q(id__in=[])

            # Get consultant experience projects
            projects_queryset = TcdProject.objects.filter(base_query).order_by('-id')

            # Setup pagination
            paginator = Paginator(projects_queryset, page_size)
            total_count = paginator.count
            has_next = page < paginator.num_pages

            try:
                projects_page = paginator.page(page)
            except:
                # If page is out of range, return empty results
                return service_success_response(data={
                    'results': [],
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total_count': 0,
                        'has_next': False
                    }
                })

            # Step 5: Build results with filtered sectors, skills, services
            results = []
            for project in projects_page:
                # Get sectors that match target sectors (comma-separated string)
                sectors = ConsultantExperienceService._get_filtered_project_sectors(project.id, target_sectors, language)

                # Get skills that match target skills (comma-separated string)
                skills = ConsultantExperienceService._get_filtered_project_skills(project.id, target_skills, language)

                # Get services that match target services (comma-separated string)
                services = ConsultantExperienceService._get_filtered_project_services(project.id, target_services, language)

                # Format dates in d MMM yyyy format
                period = ConsultantExperienceService._format_project_period(project.start_date, project.stop_date, language)

                # Keep original field names for backward compatibility
                project_data = {
                    'project_id': int(project.id),
                    'project_name': project.name or '',
                    'start_date': project.start_date.strftime('%Y-%m-%d') if project.start_date else '',
                    'stop_date': project.stop_date.strftime('%Y-%m-%d') if project.stop_date else '',
                    'period': period,  # New field with formatted period
                    'sectors': sectors,
                    'skills': skills,
                    'services': services,
                    'total_project_value': project.total_project_value,
                    'total_contract_value': project.total_contract_value,
                    'purpost': project.purpost or '',
                    'activity': project.activity or '',
                    'place': project.place or '',
                    'contract_no': project.contract_no or '',
                }

                results.append(project_data)

            return service_success_response(data={
                'results': results,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'has_next': has_next
                }
            })

        except Exception as e:
            logger.error(f"Error in get_consultant_experience: {str(e)}")
            return service_error_response(
                error_code=5000
            )

    @staticmethod
    def _get_consultant_general_data(consultant_id, consult_type, corporate_type_id):
        """
        Get general data field and ID based on consultant type
        
        Args:
            consultant: TcdUserConsult instance
            
        Returns:
            tuple: (field_name, field_value)
        """
        try:
            if consult_type == 1:
                # Individual consultant
                personal_data = TcdPersonalGeneralData.objects.filter(user_consult_id=consultant_id).first()
                if personal_data:
                    return 'personal_general_data_id'
            elif consult_type == 2:
                # Corporate consultant
                if corporate_type_id == 1:
                    corporate_data = TcdCorporateGeneralData.objects.filter(user_consult_id=consultant_id).first()
                    if corporate_data:
                        return 'corporate_general_data_id'
                else:
                    nonprofit_data = TcdNoProfitGeneralData.objects.filter(user_consult_id=consultant_id).first()
                    if nonprofit_data:
                        return 'no_profit_general_data_id'
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting consultant general data: {str(e)}")
            return None

    @staticmethod
    def _get_project_sectors_orm(project_id):
        """
        Get all sectors for a project using Django ORM
        
        Args:
            project_id (int): Project ID
            
        Returns:
            str: Comma-separated sector codes
        """
        try:
            from search.models import TcdSector
            
            # Get sector IDs from project sectors
            sector_ids = TcdProjectSector.objects.filter(
                project_id=project_id
            ).values_list('sector_id', flat=True)
            
            # Get sector codes
            sectors = TcdSector.objects.filter(
                id__in=sector_ids
            ).values_list('code', flat=True).order_by('code')
            
            return ', '.join([sector for sector in sectors if sector])
                
        except Exception as e:
            logger.error(f"Error getting project sectors: {str(e)}")
            return ''

    @staticmethod
    def _get_project_skills_orm(project_id):
        """
        Get all skills for a project using Django ORM
        
        Args:
            project_id (int): Project ID
            
        Returns:
            str: Comma-separated skill codes
        """
        try:
            # Get skill IDs from project skills
            skill_ids = TcdProjectSkill.objects.filter(
                project_id=project_id
            ).values_list('skill_id', flat=True)
            
            # Get skill codes
            skills = TcdSkill.objects.filter(
                id__in=skill_ids
            ).values_list('code', flat=True).order_by('code')
            
            return ', '.join([skill for skill in skills if skill])
                
        except Exception as e:
            logger.error(f"Error getting project skills: {str(e)}")
            return ''

    @staticmethod
    def _get_project_services_orm(project_id):
        """
        Get all services for a project using Django ORM

        Args:
            project_id (int): Project ID

        Returns:
            str: Comma-separated service codes
        """
        try:
            # Get service IDs from project services
            service_ids = TcdProjectService.objects.filter(
                project_id=project_id
            ).values_list('service_id', flat=True)

            # Get service codes
            services = TcdService.objects.filter(
                id__in=service_ids
            ).values_list('code', flat=True).order_by('code')

            return ', '.join([service for service in services if service])

        except Exception as e:
            logger.error(f"Error getting project services: {str(e)}")
            return ''

    @staticmethod
    def _get_filtered_project_sectors(project_id, target_sectors, language='th'):
        """
        Get sectors for a project filtered by target sectors

        Args:
            project_id (int): Project ID
            target_sectors (list): List of target sector IDs to filter
            language (str): Language preference ('th' or 'en')

        Returns:
            str: Comma-separated sector codes that match target sectors
        """
        try:
            from search.models import TcdSector

            # Get sector IDs from project sectors
            sector_ids = TcdProjectSector.objects.filter(
                project_id=project_id
            ).values_list('sector_id', flat=True)

            # Filter by target sectors if provided
            if target_sectors:
                sector_ids = [sid for sid in sector_ids if sid in target_sectors]

            if not sector_ids:
                return ''

            # Get sector codes
            sectors = TcdSector.objects.filter(
                id__in=sector_ids
            ).values_list('code', flat=True).order_by('code')

            return ', '.join([sector for sector in sectors if sector])

        except Exception as e:
            logger.error(f"Error getting filtered project sectors: {str(e)}")
            return ''

    @staticmethod
    def _get_filtered_project_skills(project_id, target_skills, language='th'):
        """
        Get skills for a project filtered by target skills

        Args:
            project_id (int): Project ID
            target_skills (list): List of target skill IDs to filter
            language (str): Language preference ('th' or 'en')

        Returns:
            str: Comma-separated skill codes that match target skills
        """
        try:
            # Get skill IDs from project skills
            skill_ids = TcdProjectSkill.objects.filter(
                project_id=project_id
            ).values_list('skill_id', flat=True)

            # Filter by target skills if provided
            if target_skills:
                skill_ids = [sid for sid in skill_ids if sid in target_skills]

            if not skill_ids:
                return ''

            # Get skill codes
            skills = TcdSkill.objects.filter(
                id__in=skill_ids
            ).values_list('code', flat=True).order_by('code')

            return ', '.join([skill for skill in skills if skill])

        except Exception as e:
            logger.error(f"Error getting filtered project skills: {str(e)}")
            return ''

    @staticmethod
    def _get_filtered_project_services(project_id, target_services, language='th'):
        """
        Get services for a project filtered by target services

        Args:
            project_id (int): Project ID
            target_services (list): List of target service IDs to filter
            language (str): Language preference ('th' or 'en')

        Returns:
            str: Comma-separated service codes that match target services
        """
        try:
            # Get service IDs from project services
            service_ids = TcdProjectService.objects.filter(
                project_id=project_id
            ).values_list('service_id', flat=True)

            # Filter by target services if provided
            if target_services:
                service_ids = [sid for sid in service_ids if sid in target_services]

            if not service_ids:
                return ''

            # Get service codes
            services = TcdService.objects.filter(
                id__in=service_ids
            ).values_list('code', flat=True).order_by('code')

            return ', '.join([service for service in services if service])

        except Exception as e:
            logger.error(f"Error getting filtered project services: {str(e)}")
            return ''



    @staticmethod
    def _format_project_period(start_date, stop_date, language='th'):
        """
        Format project period in d MMM yyyy format

        Args:
            start_date (datetime): Project start date
            stop_date (datetime): Project stop date
            language (str): Language preference ('th' or 'en')

        Returns:
            str: Formatted period string
        """
        try:
            if not start_date or not stop_date:
                return ''

            # Month names mapping
            if language == 'en':
                months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                         'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            else:
                months = ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.',
                         'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.']

            # Format start date
            start_formatted = f"{start_date.day} {months[start_date.month - 1]} {start_date.year}"

            # Format stop date
            stop_formatted = f"{stop_date.day} {months[stop_date.month - 1]} {stop_date.year}"

            return f"{start_formatted} - {stop_formatted}"

        except Exception as e:
            logger.error(f"Error formatting project period: {str(e)}")
            return ''


class ConsultantSuitableProjectsService:
    """
    Service for getting suitable projects for consultants
    """

    @staticmethod
    def get_suitable_projects(user_consult_id, sector_id=None, sort_by_matching=False, page=1, page_size=10, language='th'):
        """
        Get list of suitable projects for a consultant
        
        Args:
            user_consult_id (int): Consultant user ID
            sector_id (int, optional): Sector ID for filtering
            sort_by_matching (bool): Whether to sort by matching result
            page (int): Page number for pagination
            page_size (int): Number of items per page
            
        Returns:
            dict: Service response with suitable projects data
        """
        try:
            from django.db.models import Q
            from django.utils import timezone
            from datetime import datetime
            
            # Current date for filtering
            now = timezone.now()
            
            # Build base query
            queryset = TcdAppProjectConsult.objects.filter(
                user_consult_id=user_consult_id
            )
            
            # Use raw SQL to get all data with proper filtering and sorting
            from django.db import connection
            
            # Get the table names
            project_consult_table = TcdAppProjectConsult._meta.db_table
            project_table = TcdAppProject._meta.db_table
            project_sector_table = TcdAppProjectSector._meta.db_table
            
            # Build the base SQL query
            sql_query = f"""
                SELECT pc.*
                FROM {project_consult_table} pc
                INNER JOIN {project_table} p ON pc.app_project_id = p.id
                WHERE pc.user_consult_id = %s
                AND pc.matching >= COALESCE(p.result, 0)
                AND p.status = '1'
            """
            
            params = [user_consult_id]
            
            # Add sector filter if provided
            if sector_id is not None and sector_id != "":
                sql_query += f"""
                    AND EXISTS (
                        SELECT 1 FROM {project_sector_table} ps 
                        WHERE ps.app_project_id = p.id 
                        AND ps.sector_id = %s
                    )
                """
                params.append(sector_id)
            
            # Add sorting
            if sort_by_matching:
                sql_query += " ORDER BY pc.matching DESC, p.start_date ASC"
            else:
                sql_query += " ORDER BY p.id DESC"
            
            # Add pagination (SQL Server syntax)
            offset = (page - 1) * page_size
            sql_query += f" OFFSET {offset} ROWS FETCH NEXT {page_size} ROWS ONLY"
            
            # Execute the query to get paginated results
            with connection.cursor() as cursor:
                cursor.execute(sql_query, params)
                columns = [col[0] for col in cursor.description]
                projects = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            # Get total count for pagination
            count_query = f"""
                SELECT COUNT(*)
                FROM {project_consult_table} pc
                INNER JOIN {project_table} p ON pc.app_project_id = p.id
                WHERE pc.user_consult_id = %s
                AND pc.matching >= COALESCE(p.result, 0)
                AND p.status = '1'
            """
            
            count_params = [user_consult_id]
            
            if sector_id is not None and sector_id != "":
                count_query += f"""
                    AND EXISTS (
                        SELECT 1 FROM {project_sector_table} ps 
                        WHERE ps.app_project_id = p.id 
                        AND ps.sector_id = %s
                    )
                """
                count_params.append(sector_id)
            
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                total_count = cursor.fetchone()[0]
            
            # Calculate pagination
            has_next = (page * page_size) < total_count
            
            # Build response data
            results = []
            for project_consult_data in projects:
                try:
                    # Get related project and member
                    project = TcdAppProject.objects.filter(id=project_consult_data['app_project_id']).first()
                    member = TcdAppMember.objects.filter(id=project_consult_data['app_member_id']).first()
                    project_consult = TcdAppProjectConsult.objects.filter(id=project_consult_data['id'],user_consult_id=user_consult_id).first()
                    
                    if not project or not member:
                        continue
                    
                    # Build organization name
                    organization_name = ConsultantSuitableProjectsService._build_organization_name(member, language)
                    
                    project_data = {
                        'project_id': int(project.id),
                        'project_name': project.name or '',
                        'organization_name': organization_name,
                        'announcement_date_start': project.start_date,
                        'announcement_date_end': project.end_date,
                        'favorite_status': project_consult_data.get('consult_favorite', '0') or '0',
                        'matching_result': float(project_consult_data.get('matching', 0)),
                        'view_count': project_consult.member_view or 0
                    }
                    
                    results.append(project_data)
                    
                except Exception as e:
                    logger.error(f"Error processing project {project_consult_data.get('id', 'unknown')}: {str(e)}")
                    continue
            
            return service_success_response(data={
                'results': results,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'has_next': has_next
                }
            })
            
        except Exception as e:
            logger.error(f"Error getting suitable projects for consultant {user_consult_id}: {str(e)}")
            return service_error_response(
                error_code=5000
            )
    
    @staticmethod
    def _build_organization_name(member, language='th'):
        """
        Build organization name with government sector details
        
        Args:
            member: TcdAppMember instance
            
        Returns:
            str: Complete organization name
        """
        try:
            organization_name = member.name or ''
            # logger.info(f'organization_name : {organization_name}')
            
            # Add department info
            if member.app_mas_department_id == 0 or member.app_mas_department_id is None:
                # Other department
                if member.app_mas_department_other:
                    organization_name += f" / {member.app_mas_department_other}"
                    # logger.info(f"organization_name: {organization_name}")
            else:
                # Get department name from foreign key
                try:
                    from authentication.models import TcdAppMasDepartment
                    department = TcdAppMasDepartment.objects.filter(id=member.app_mas_department_id).first()
                    if department:
                        if language == 'th':
                            dept_name = department.name_th
                        else:
                            dept_name = department.name_en
                        if dept_name:
                            logger.info(f"dept_name: {dept_name}")
                            organization_name += f" / {dept_name}"
                except:
                    pass
            
            # Add ministry info
            if member.app_mas_ministry_id == 0 or member.app_mas_ministry_id is None:
                # Other ministry
                if member.app_mas_ministry_other:
                    logger.info(f"member.app_mas_ministry_other: {member.app_mas_ministry_other}")
                    organization_name += f" / {member.app_mas_ministry_other}"
            else:
                # Get ministry name from foreign key
                try:
                    from authentication.models import TcdAppMasMinistry
                    ministry = TcdAppMasMinistry.objects.filter(id=member.app_mas_ministry_id).first()
                    if ministry:
                        if language == 'th':
                            ministry_name = ministry.name_th
                        else:
                            ministry_name = ministry.name_en
                        if ministry_name:
                            logger.info(f"ministry_name: {ministry_name}")
                            organization_name += f" / {ministry_name}"
                except:
                    pass
            
            # Add government sector info
            if member.app_mas_government_sector_id == 0 or member.app_mas_government_sector_id is None:
                # Other government sector
                if member.app_mas_government_sector_other:
                    logger.info(f"member.app_mas_government_sector_other: {member.app_mas_government_sector_other}")
                    organization_name += f" / {member.app_mas_government_sector_other}"
            else:
                # Get government sector name from foreign key
                try:
                    from authentication.models import TcdAppMasGovernmentSector
                    gov_sector = TcdAppMasGovernmentSector.objects.filter(id=member.app_mas_government_sector_id).first()
                    if gov_sector:
                        if language == 'th':
                            sector_name = gov_sector.name_th
                        else:
                            sector_name = gov_sector.name_en
                        if sector_name:
                            logger.info(f"sector_name: {sector_name}")
                            organization_name += f" / {sector_name}"
                except:
                    pass
            
            return organization_name
            
        except Exception as e:
            logger.error(f"Error building organization name: {str(e)}")
            return member.name or 'Unknown Organization'
    
    @staticmethod
    def _format_date_range(start_date, end_date, language='th'):
        """
        Format date range in d MMM yyyy format
        
        Args:
            start_date: Start date
            end_date: End date
            language: Language for formatting ('th' or 'en')
            
        Returns:
            str: Formatted date range
        """
        try:
            if not start_date or not end_date:
                return ''
            
            # Thai month names
            thai_months = {
                1: 'ม.ค.', 2: 'ก.พ.', 3: 'มี.ค.', 4: 'เม.ย.',
                5: 'พ.ค.', 6: 'มิ.ย.', 7: 'ก.ค.', 8: 'ส.ค.',
                9: 'ก.ย.', 10: 'ต.ค.', 11: 'พ.ย.', 12: 'ธ.ค.'
            }
            
            # English month names
            eng_months = {
                1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr',
                5: 'May', 6: 'Jun', 7: 'Jul', 8: 'Aug',
                9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'
            }
            
            # Choose month names based on language
            months = thai_months if language == 'th' else eng_months
            
            # Format start date
            start_day = start_date.day
            start_month = months.get(start_date.month, str(start_date.month))
            start_year = start_date.year
            
            # Format end date
            end_day = end_date.day
            end_month = months.get(end_date.month, str(end_date.month))
            end_year = end_date.year
            
            return f"{start_day} {start_month} {start_year} - {end_day} {end_month} {end_year}"
            
        except Exception as e:
            logger.error(f"Error formatting date range: {str(e)}")
            return ''


class ConsultantFavoriteProjectsService:
    """
    Service for getting favorite projects for consultants (consult_favorite = '1')
    """

    @staticmethod
    def get_consultant_favorite_projects(user_consult_id, sort_by_matching=False, page=1, page_size=10, language='th'):
        """
        Get favorite projects for a consultant based on consult_favorite = '1'
        
        Args:
            user_consult_id (int): Consultant user ID who is logged in
            sort_by_matching (bool): If True, sort by matching DESC, start_date ASC. Otherwise, sort by id DESC
            page (int): Page number (default: 1)
            page_size (int): Number of items per page (default: 10)
            language (str): Language for date formatting ('th' or 'en')
            
        Returns:
            dict: Service response with favorite projects data and pagination
        """
        try:
            from django.core.paginator import Paginator
            from django.db import connection
            from django.utils import timezone
            
            # Validate inputs
            if page < 1:
                page = 1
            if page_size < 1:
                page_size = 10
            elif page_size > 100:
                page_size = 100
            
            # Build the base query based on the requirement
            # SELECT FROM app_project_consult 
            # INNER JOIN app_project ON app_project_id = app_project_consult.app_project_id
            # WHERE app_project_consult.user_consult_id = <user_consult_id>
            # AND app_project_consult.consult_favorite = '1' AND app_project.status = '1'
            
            base_query = """
                SELECT 
                    pc.id,
                    pc.app_project_id,
                    pc.app_member_id,
                    pc.user_consult_id,
                    pc.consult_favorite,
                    pc.matching,
                    pc.member_view,
                    pc.consult_view,
                    p.name as project_name,
                    p.start_date,
                    p.end_date
                FROM tcd_app_project_consult pc
                INNER JOIN tcd_app_project p ON p.id = pc.app_project_id
                WHERE pc.user_consult_id = %s
                AND pc.consult_favorite = '1'
                AND p.status = '1'
            """
            
            # Add sorting
            if sort_by_matching:
                # ถ้าเลือก "ผลการจับคู่จาก มาก-น้อย" : ORDER BY app_project_consult.matching DESC, app_project.start_date ASC
                base_query += " ORDER BY pc.matching DESC, p.start_date ASC"
            else:
                # ถ้าไม่ใช่ : ORDER BY app_project_consult.id DESC
                base_query += " ORDER BY pc.id DESC"
            
            # Get total count for pagination
            count_query = """
                SELECT COUNT(*)
                FROM tcd_app_project_consult pc
                INNER JOIN tcd_app_project p ON p.id = pc.app_project_id
                WHERE pc.user_consult_id = %s
                AND pc.consult_favorite = '1'
                AND p.status = '1'
            """
            
            with connection.cursor() as cursor:
                # Get total count
                cursor.execute(count_query, [user_consult_id])
                total_count = cursor.fetchone()[0]
                
                # Apply pagination
                offset = (page - 1) * page_size
                paginated_query = base_query + f" OFFSET {offset} ROWS FETCH NEXT {page_size} ROWS ONLY"
                
                # Execute paginated query
                cursor.execute(paginated_query, [user_consult_id])
                columns = [col[0] for col in cursor.description]
                project_consults = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            # Calculate pagination info
            has_next = (page * page_size) < total_count
            
            # Build response data
            results = []
            for project_consult_data in project_consults:
                try:
                    # Get member information for organization name
                    member = TcdAppMember.objects.filter(id=project_consult_data['app_member_id']).first()
                    
                    # Build organization name (ชื่อหน่วยงาน)
                    organization_name = ''
                    if member:
                        organization_name = ConsultantFavoriteProjectsService._build_organization_name(member, language)
                    
                    # Format announcement date (วันที่ประกาศ)
                    announcement_date = ''
                    if project_consult_data['start_date'] and project_consult_data['end_date']:
                        announcement_date = ConsultantFavoriteProjectsService._format_date_range(
                            project_consult_data['start_date'], 
                            project_consult_data['end_date'], 
                            language
                        )
                    
                    project_data = {
                        'project_id': int(project_consult_data['app_project_id']),
                        'project_name': project_consult_data['project_name'] or '',  # ชื่อโครงการ
                        'organization_name': organization_name,  # ชื่อหน่วยงาน
                        'announcement_date': announcement_date,  # วันที่ประกาศ (start_date - end_date)
                        'favorite_status': project_consult_data['consult_favorite'],  # สถานะการถูกใจ (1 : กดถูกใจ, 0 : ไม่ได้กดถูกใจ)
                        'matching_result': float(project_consult_data['matching']) if project_consult_data['matching'] else 0.0,  # ผลจับคู่
                        'view_count': project_consult_data['member_view'] or 0  # จำนวนการดู
                    }
                    
                    results.append(project_data)
                    
                except Exception as e:
                    logger.error(f"Error processing favorite project {project_consult_data.get('id', 'unknown')}: {str(e)}")
                    continue
            
            return service_success_response(data={
                'results': results,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'has_next': has_next
                }
            })
            
        except Exception as e:
            logger.error(f"Error getting favorite projects for consultant {user_consult_id}: {str(e)}")
            return service_error_response(
                error_code=5000
            )
    
    @staticmethod
    def _build_organization_name(member, language='th'):
        """
        Build organization name with government sector details
        ดึงข้อมูลอ้างอิงตามข้อ 3 ส่วนข้อมูลหน่วยงาน : ชื่อหน่วยงาน
        
        Args:
            member: TcdAppMember instance
            
        Returns:
            str: Complete organization name
        """
        try:
            organization_name = member.name or ''
            
            # Add department info
            if member.app_mas_department_id == 0 or member.app_mas_department_id is None:
                # Other department
                if member.app_mas_department_other:
                    organization_name += f" / {member.app_mas_department_other}"
                    # logger.info(f"organization_name: {organization_name}")
            else:
                # Get department name from foreign key
                try:
                    from authentication.models import TcdAppMasDepartment
                    department = TcdAppMasDepartment.objects.filter(id=member.app_mas_department_id).first()
                    if department:
                        if language == 'th':
                            dept_name = department.name_th
                        else:
                            dept_name = department.name_en
                        if dept_name:
                            logger.info(f"dept_name: {dept_name}")
                            organization_name += f" / {dept_name}"
                except:
                    pass
            
            # Add ministry info
            if member.app_mas_ministry_id == 0 or member.app_mas_ministry_id is None:
                # Other ministry
                if member.app_mas_ministry_other:
                    logger.info(f"member.app_mas_ministry_other: {member.app_mas_ministry_other}")
                    organization_name += f" / {member.app_mas_ministry_other}"
            else:
                # Get ministry name from foreign key
                try:
                    from authentication.models import TcdAppMasMinistry
                    ministry = TcdAppMasMinistry.objects.filter(id=member.app_mas_ministry_id).first()
                    if ministry:
                        if language == 'th':
                            ministry_name = ministry.name_th
                        else:
                            ministry_name = ministry.name_en
                        if ministry_name:
                            logger.info(f"ministry_name: {ministry_name}")
                            organization_name += f" / {ministry_name}"
                except:
                    pass
            
            # Add government sector info
            if member.app_mas_government_sector_id == 0 or member.app_mas_government_sector_id is None:
                # Other government sector
                if member.app_mas_government_sector_other:
                    logger.info(f"member.app_mas_government_sector_other: {member.app_mas_government_sector_other}")
                    organization_name += f" / {member.app_mas_government_sector_other}"
            else:
                # Get government sector name from foreign key
                try:
                    from authentication.models import TcdAppMasGovernmentSector
                    gov_sector = TcdAppMasGovernmentSector.objects.filter(id=member.app_mas_government_sector_id).first()
                    if gov_sector:
                        if language == 'th':
                            sector_name = gov_sector.name_th
                        else:
                            sector_name = gov_sector.name_en
                        if sector_name:
                            logger.info(f"sector_name: {sector_name}")
                            organization_name += f" / {sector_name}"
                except:
                    pass
            
            return organization_name
            
        except Exception as e:
            logger.error(f"Error building organization name: {str(e)}")
            return member.name or 'Unknown Organization'
    
    @staticmethod
    def _format_date_range(start_date, end_date, language='th'):
        """
        Format date range in d MMM yyyy format
        
        Args:
            start_date: Start date
            end_date: End date
            language: Language for formatting ('th' or 'en')
            
        Returns:
            str: Formatted date range
        """
        try:
            if not start_date or not end_date:
                return ''
            
            # Thai month names
            thai_months = {
                1: 'ม.ค.', 2: 'ก.พ.', 3: 'มี.ค.', 4: 'เม.ย.',
                5: 'พ.ค.', 6: 'มิ.ย.', 7: 'ก.ค.', 8: 'ส.ค.',
                9: 'ก.ย.', 10: 'ต.ค.', 11: 'พ.ย.', 12: 'ธ.ค.'
            }
            
            # English month names
            eng_months = {
                1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr',
                5: 'May', 6: 'Jun', 7: 'Jul', 8: 'Aug',
                9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'
            }
            
            # Choose month names based on language
            months = thai_months if language == 'th' else eng_months
            
            # Format start date
            start_day = start_date.day
            start_month = months.get(start_date.month, str(start_date.month))
            start_year = start_date.year
            
            # Format end date
            end_day = end_date.day
            end_month = months.get(end_date.month, str(end_date.month))
            end_year = end_date.year
            
            return f"{start_day} {start_month} {start_year} - {end_day} {end_month} {end_year}"
            
        except Exception as e:
            logger.error(f"Error formatting date range: {str(e)}")
            return ''


class ProjectDetailExtendedService:
    """
    Service for getting extended project details with all master data relationships
    """

    @staticmethod
    def get_project_detail_extended(app_project_id, user_consult_id=None, language='th'):
        """
        Get detailed project information with all master data relationships
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int, optional): Consultant user ID for matching data
            language (str): Language preference ('th' or 'en')
            
        Returns:
            dict: Service response with detailed project data
        """
        try:
            from django.db import connection
            from django.conf import settings
            import os
            
            # Main project query with all master data joins
            sql_query = """
                SELECT 
                    ap.*,
                    amce.id as consult_exp_id,
                    amce.name_th as consult_exp_name_th,
                    amce.name_en as consult_exp_name_en,
                    amr.id as result_id,
                    amr.name_th as result_name_th,
                    amr.name_en as result_name_en,
                    ampc.id as project_cost_id,
                    ampc.name_th as project_cost_name_th,
                    ampc.name_en as project_cost_name_en,
                    ampn.id as project_number_id,
                    ampn.name_th as project_number_name_th,
                    ampn.name_en as project_number_name_en,
                    ampn_sector.id as project_sector_id,
                    ampn_sector.name_th as project_sector_name_th,
                    ampn_sector.name_en as project_sector_name_en,
                    ampt.id as project_type_id,
                    ampt.name_th as project_type_name_th,
                    ampt.name_en as project_type_name_en,
                    amcn.id as consult_number_id,
                    amcn.name_th as consult_number_name_th,
                    amcn.name_en as consult_number_name_en,
                    amct.id as consult_type_id,
                    amct.name_th as consult_type_name_th,
                    amct.name_en as consult_type_name_en,
                    amcr.id as consult_rating_id,
                    amcr.name_th as consult_rating_name_th,
                    amcr.name_en as consult_rating_name_en,
                    amcc.id as consult_certificate_id,
                    amcc.name_th as consult_certificate_name_th,
                    amcc.name_en as consult_certificate_name_en,
                    am.phone as member_phone,
                    am.email as member_email,
                    am.website as member_website,
                    am.app_mas_member_type_id as member_type_id
                FROM tcd_app_project ap
                LEFT JOIN tcd_app_mas_consult_exp amce ON ap.consult_exp = amce.id
                LEFT JOIN tcd_app_mas_result amr ON ap.result = amr.id
                LEFT JOIN tcd_app_mas_project_cost ampc ON ap.project_cost = ampc.id
                LEFT JOIN tcd_app_mas_project_number ampn ON ap.project_number = ampn.id
                LEFT JOIN tcd_app_mas_project_number ampn_sector ON ap.project_sector = ampn_sector.id
                LEFT JOIN tcd_app_mas_project_type ampt ON ap.project_type = ampt.id
                LEFT JOIN tcd_app_mas_consult_number amcn ON ap.consult_number = amcn.id
                LEFT JOIN tcd_app_mas_consult_type amct ON ap.consult_type = amct.id
                LEFT JOIN tcd_app_mas_consult_rating amcr ON ap.consult_rating = amcr.id
                LEFT JOIN tcd_app_mas_consult_certificate amcc ON ap.consult_certificate = amcc.id
                LEFT JOIN tcd_app_member am ON ap.app_member_id = am.id
                WHERE ap.id = %s
            """
            
            with connection.cursor() as cursor:
                cursor.execute(sql_query, [app_project_id])
                row = cursor.fetchone()
                
                if not row:
                    return service_error_response(
                        error_code=3002,
                        message="Project not found"
                    )
                
                # Get column names
                columns = [col[0] for col in cursor.description]
                result = dict(zip(columns, row))
                
                # Increment view count
                cursor.execute("UPDATE tcd_app_project SET [view] = [view] + 1 WHERE id = %s", [app_project_id])
                
                # Get organization data using existing method
                organization_data = ProjectDetailExtendedService._get_organization_data(result, language)
                
                # Get consultant matching data if user_consult_id provided
                matching_data = None
                contact_button_data = None
                if user_consult_id:
                    matching_data = ProjectDetailExtendedService._get_consultant_matching_data(app_project_id, user_consult_id)
                    contact_button_data = ProjectDetailExtendedService._get_contact_button_data(app_project_id, user_consult_id)
                
                # Get sectors and skills data
                sectors_skills_data = ProjectDetailExtendedService._get_sectors_and_skills(app_project_id)
                
                # Get services data
                services_data = ProjectDetailExtendedService._get_services(app_project_id)
                
                # Format dates
                project_period = ProjectDetailExtendedService._format_date_range(
                    result['period_start'], result['period_end'], language
                )
                announcement_period = ProjectDetailExtendedService._format_date_range(
                    result['start_date'], result['end_date'], language
                )
                
                # Build document download URL
                document_url = None
                if result['ref']:
                    # Assuming document folder path from settings or default
                    base_file_url = getattr(settings, 'BASE_FILE_URL')
                    document_url = base_file_url + result['ref']
                
                # Format the response data
                project_data = {
                    # Basic project information
                    'project_id': result['id'],
                    'project_name': result['name'],
                    'view_count': result['view'] + 1,  # Already incremented
                    'purpose': result['purpost'],  # วัตถุประสงค์โครงการ
                    'activity': result['activity'],  # ขอบเขตการดำเนินงาน
                    'project_period': project_period,  # ระยะเวลาโครงการ
                    'announcement_period': announcement_period,  # วันที่ประกาศโครงการ
                    'keyword': result['keyword'],  # คำค้นหา (Keyword)
                    'document_url': document_url,  # ปุ่ม ดาวน์โหลดเอกสาร
                    
                    # Organization data (ข้อมูลหน่วยงาน)
                    'organization': organization_data,
                    
                    # Consultant matching data (only for consultants)
                    'matching_result': matching_data,
                    'contact_button': contact_button_data,
                    
                    # Sectors and skills data (ข้อมูลสาขา ความเชี่ยวชาญ)
                    'sectors_skills': sectors_skills_data,
                    
                    # Services data (การบริการ)
                    'services': services_data,
                    
                    # Master data with language support
                    'project_sector_count': ProjectDetailExtendedService._get_display_name(
                        result, 'project_sector_name', language
                    ),  # จำนวนโครงการที่สอดคล้องกับสาขา
                    'consultant_experience': ProjectDetailExtendedService._get_display_name(
                        result, 'consult_exp_name', language
                    ),  # ประสบการณ์ที่ปรึกษา
                    'project_cost': ProjectDetailExtendedService._get_display_name(
                        result, 'project_cost_name', language
                    ),  # มูลค่าสัญญาจ้างที่ปรึกษา
                    'project_number': ProjectDetailExtendedService._get_display_name(
                        result, 'project_number_name', language
                    ),  # จำนวนโครงการ
                    'project_type': ProjectDetailExtendedService._get_display_name(
                        result, 'project_type_name', language
                    ),  # ประเภทโครงการ
                    'consultant_number': ProjectDetailExtendedService._get_display_name(
                        result, 'consult_number_name', language
                    ),  # จำนวนบุคลากรที่ปรึกษา
                    'consultant_type': ProjectDetailExtendedService._get_display_name(
                        result, 'consult_type_name', language
                    ),  # ประเภทที่ปรึกษา
                    'consultant_rating': ProjectDetailExtendedService._get_display_name(
                        result, 'consult_rating_name', language
                    ),  # ระดับที่ปรึกษา
                    'consultant_certificate': ProjectDetailExtendedService._get_display_name(
                        result, 'consult_certificate_name', language
                    ),  # หนังสือรับรองการขึ้นทะเบียน
                    'matching_result_master': ProjectDetailExtendedService._get_display_name(
                        result, 'result_name', language
                    ),  # ผลการจับคู่
                }
                
                return service_success_response(data=project_data)
                
        except Exception as e:
            logger.error(f"Error getting extended project detail: {str(e)}")
            return service_error_response(
                error_code=5000
            )
    
    @staticmethod
    def _get_display_name(result, field_prefix, language):
        """Get display name based on language preference"""
        if language == 'en':
            return result.get(f'{field_prefix}_en')
        return result.get(f'{field_prefix}_th')
    
    @staticmethod
    def _get_organization_data(result, language):
        """Get organization data from member information"""
        try:
            from .models import TcdAppMember
            from authentication.models import TcdAppMasDepartment, TcdAppMasMinistry, TcdAppMasGovernmentSector
            
            member = TcdAppMember.objects.filter(id=result['app_member_id']).first()
            if not member:
                return None
            
            # Start with base organization name
            org_name = member.name or ''
            
            # If government sector (app_mas_member_type_id = 1)
            if getattr(member, 'app_mas_member_type_id', None) == 1:
                # Add department info
                if getattr(member, 'app_mas_department_id', None) is not None:
                    if member.app_mas_department_id == 0:
                        # Other department
                        if getattr(member, 'app_mas_department_other', None):
                            org_name += f" / {member.app_mas_department_other}"
                    else:
                        # Get department name from foreign key
                        try:
                            department = TcdAppMasDepartment.objects.filter(id=member.app_mas_department_id).first()
                            if department:
                                dept_name = department.name_en if language == 'en' else department.name_th
                                if dept_name:
                                    org_name += f" / {dept_name}"
                        except Exception as dept_error:
                            logger.warning(f"Error getting department info: {str(dept_error)}")
                
                # Add ministry info
                if getattr(member, 'app_mas_ministry_id', None) is not None:
                    if member.app_mas_ministry_id == 0:
                        # Other ministry
                        if getattr(member, 'app_mas_ministry_other', None):
                            org_name += f" / {member.app_mas_ministry_other}"
                    else:
                        # Get ministry name from foreign key
                        try:
                            ministry = TcdAppMasMinistry.objects.filter(id=member.app_mas_ministry_id).first()
                            if ministry:
                                ministry_name = ministry.name_en if language == 'en' else ministry.name_th
                                if ministry_name:
                                    org_name += f" / {ministry_name}"
                        except Exception as ministry_error:
                            logger.warning(f"Error getting ministry info: {str(ministry_error)}")
                
                # Add government sector info
                if getattr(member, 'app_mas_government_sector_id', None) is not None:
                    if member.app_mas_government_sector_id == 0:
                        # Other government sector
                        if getattr(member, 'app_mas_government_sector_other', None):
                            org_name += f" / {member.app_mas_government_sector_other}"
                    else:
                        # Get government sector name from foreign key
                        try:
                            gov_sector = TcdAppMasGovernmentSector.objects.filter(id=member.app_mas_government_sector_id).first()
                            if gov_sector:
                                sector_name = gov_sector.name_en if language == 'en' else gov_sector.name_th
                                if sector_name:
                                    org_name += f" / {sector_name}"
                        except Exception as sector_error:
                            logger.warning(f"Error getting government sector info: {str(sector_error)}")
            
            # Determine organization type
            if result['member_type_id'] == 1:
                org_type = "หน่วยงานภาครัฐ" if language == 'th' else "Government Agency"
            else:
                org_type = "หน่วยงานภาคเอกชน" if language == 'th' else "Private Organization"
            
            return {
                'name': org_name,
                'phone': result['member_phone'],
                'email': result['member_email'],
                'type': org_type,
                'website': result['member_website']
            }
            
        except Exception as e:
            logger.error(f"Error getting organization data: {str(e)}")
            return None
    
    @staticmethod
    def _get_consultant_matching_data(app_project_id, user_consult_id):
        """Get consultant matching data if consultant is matched with project"""
        try:
            from django.db import connection
            
            sql_query = """
                SELECT matching, consult_send 
                FROM tcd_app_project_consult
                WHERE user_consult_id = %s AND app_project_id = %s
            """
            
            with connection.cursor() as cursor:
                cursor.execute(sql_query, [user_consult_id, app_project_id])
                row = cursor.fetchone()
                
                if row:
                    return {
                        'matching_score': row[0],
                        'consult_send': row[1]
                    }
                return None
                
        except Exception as e:
            logger.error(f"Error getting consultant matching data: {str(e)}")
            return None
    
    @staticmethod
    def _get_contact_button_data(app_project_id, user_consult_id):
        """Get contact button data for consultant"""
        try:
            from django.db import connection
            
            sql_query = """
                SELECT consult_send 
                FROM tcd_app_project_consult
                WHERE user_consult_id = %s AND app_project_id = %s
            """
            
            with connection.cursor() as cursor:
                cursor.execute(sql_query, [user_consult_id, app_project_id])
                row = cursor.fetchone()
                
                if row:
                    consult_send = row[0]
                    if consult_send == 1:
                        return {
                            'text': "ส่งข้อมูลติดต่อแล้ว",
                            'text_en': "Contact Information Sent",
                            'sent': True
                        }
                    else:
                        return {
                            'text': "ส่งข้อมูลติดต่อ",
                            'text_en': "Send Contact Information",
                            'sent': False
                        }
                return None
                
        except Exception as e:
            logger.error(f"Error getting contact button data: {str(e)}")
            return None
    
    @staticmethod
    def _get_sectors_and_skills(app_project_id):
        """Get sectors and skills data for the project"""
        try:
            from django.db import connection
            
            # Get sectors for the project
            sectors_query = """
                SELECT aps.id as project_sector_id, s.code, s.name_th, s.name_en
                FROM tcd_app_project_sector aps
                INNER JOIN tcd_sector s ON aps.sector_id = s.id
                WHERE aps.app_project_id = %s
            """
            
            sectors_data = []
            with connection.cursor() as cursor:
                cursor.execute(sectors_query, [app_project_id])
                sector_rows = cursor.fetchall()
                
                for sector_row in sector_rows:
                    project_sector_id, code, name_th, name_en = sector_row
                    
                    # Get skills for this sector
                    skills_query = """
                        SELECT sk.code, sk.name_th, sk.name_en
                        FROM tcd_app_project_skill aps
                        INNER JOIN tcd_skill sk ON aps.skill_id = sk.id
                        WHERE aps.app_project_id = %s AND aps.app_project_sector_id = %s
                    """
                    
                    cursor.execute(skills_query, [app_project_id, project_sector_id])
                    skill_rows = cursor.fetchall()
                    
                    skills = []
                    for skill_row in skill_rows:
                        skill_code, skill_name_th, skill_name_en = skill_row
                        skills.append({
                            'code': skill_code,
                            'name_th': skill_name_th,
                            'name_en': skill_name_en,
                            'display': f"{skill_code} : {skill_name_th} : {skill_name_en}"
                        })
                    
                    sectors_data.append({
                        'code': code,
                        'name_th': name_th,
                        'name_en': name_en,
                        'display': f"{code} : {name_th} : {name_en}",
                        'skills': skills
                    })
            
            return sectors_data
            
        except Exception as e:
            logger.error(f"Error getting sectors and skills: {str(e)}")
            return []
    
    @staticmethod
    def _get_services(app_project_id):
        """Get services data for the project"""
        try:
            from django.db import connection
            
            services_query = """
                SELECT sv.code, sv.name_th, sv.name_en
                FROM tcd_app_project_service aps
                INNER JOIN tcd_service sv ON aps.service_id = sv.id
                WHERE aps.app_project_id = %s
            """
            
            services_data = []
            with connection.cursor() as cursor:
                cursor.execute(services_query, [app_project_id])
                service_rows = cursor.fetchall()
                
                for service_row in service_rows:
                    code, name_th, name_en = service_row
                    services_data.append({
                        'code': code,
                        'name_th': name_th,
                        'name_en': name_en,
                        'display': f"{code} : {name_th} : {name_en}"
                    })
            
            return services_data
            
        except Exception as e:
            logger.error(f"Error getting services: {str(e)}")
            return []
    
    @staticmethod
    def _format_date_range(start_date, end_date, language='th'):
        """Format date range for display"""
        try:
            from datetime import datetime
            
            if not start_date or not end_date:
                return ""
            
            # Convert to datetime if they're strings
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            if language == 'en':
                # English format: d MMM yyyy
                start_str = start_date.strftime('%d %b %Y')
                end_str = end_date.strftime('%d %b %Y')
            else:
                # Thai format: d MMM yyyy (using Thai month names)
                thai_months = [
                    'ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.',
                    'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'
                ]
                start_str = f"{start_date.day} {thai_months[start_date.month-1]} {start_date.year + 543}"
                end_str = f"{end_date.day} {thai_months[end_date.month-1]} {end_date.year + 543}"
            
            return f"{start_str} - {end_str}"
            
        except Exception as e:
            logger.error(f"Error formatting date range: {str(e)}")
            return ""


class ConsultantFavoriteService:
    """
    Service for managing consultant favorite status for projects
    """

    @staticmethod
    def get_consultant_favorite_status(app_project_id, user_consult_id):
        """
        Get current consultant favorite status for a project-consultant pair
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID who is logged in
            
        Returns:
            dict: Service response with consultant favorite status data
        """
        try:
            # Find the record in app_project_consult
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            return service_success_response(data={
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'consult_favorite': project_consult.consult_favorite,
                'is_favorite': project_consult.consult_favorite == '1'
            })
            
        except Exception as e:
            logger.error(f"Error getting consultant favorite status: {str(e)}")
            return service_error_response(
                error_code=5000,
                message="System error"
            )

    @staticmethod
    def _get_consultant_display_name(consultant, user_consult_id):
        """
        Get consultant display name according to business rules
        
        Args:
            consultant: TcdUserConsult object
            user_consult_id (int): Consultant user ID
            consultant_name (str): Fallback consultant name
            
        Returns:
            str: Consultant display name
        """

        try:
            consult_type = consultant.consult_type
            if consult_type == 1:
                personal_data = TcdPersonalGeneralData.objects.filter(user_consult_id=user_consult_id).first()
                if personal_data:
                    return f"{personal_data.first_name} {personal_data.last_name}"
            else:
                if consultant.corporate_type_id and consultant.corporate_type_id == 1:
                    corporate_data = TcdCorporateGeneralData.objects.filter(user_consult_id=user_consult_id).first()
                    if corporate_data:
                        return corporate_data.name
                else:
                    no_profit_data = TcdNoProfitGeneralData.objects.filter(user_consult_id=user_consult_id).first()
                    if no_profit_data:
                        return no_profit_data.name
            
        except Exception as e:
            logger.error(f"Error getting consultant display name: {str(e)}")
            return None

    @staticmethod
    def update_consultant_favorite_status(app_project_id, user_consult_id, request=None):
        """
        Toggle consultant favorite status (0 -> 1, other -> 0)
        
        Args:
            app_project_id (int): Project ID
            user_consult_id (int): Consultant user ID who is logged in
            request: Django request object for IP address
            
        Returns:
            dict: Service response with old and new favorite status
        """
        try:
            # First, check if the record exists
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).first()
            
            if not project_consult:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            old_consult_favorite = project_consult.consult_favorite
            
            # Toggle favorite status: if "0" -> "1", else -> "0"
            new_consult_favorite = "1" if old_consult_favorite == "0" else "0"
            
            # Determine action log message
            action_log = "เพิ่มรายการที่ชอบ" if old_consult_favorite == "0" else "ยกเลิกรายการที่ชอบ"
            
            # Update the record
            updated_rows = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).update(
                consult_favorite=new_consult_favorite,
                update_date=timezone.now()
            )
            
            # Determine if update was successful
            update_success = updated_rows > 0
            remark = f"{action_log} สำเร็จ" if update_success else f"{action_log} ไม่สำเร็จ"
            
            # Log the action to TcdAppLogs
            try:
                # Get consultant information
                from authentication.models import TcdUserConsult
                consultant = TcdUserConsult.objects.filter(id=user_consult_id).first()
                consultant_name = ""
                if consultant:
                    # Try to get the consultant's name from different fields
                    if hasattr(consultant, 'username') and consultant.username:
                        consultant_name = consultant.username
                    elif hasattr(consultant, 'name') and consultant.name:
                        consultant_name = consultant.name
                    elif hasattr(consultant, 'first_name') and consultant.first_name:
                        consultant_name = consultant.first_name
                        if hasattr(consultant, 'last_name') and consultant.last_name:
                            consultant_name += f" {consultant.last_name}"
                    else:
                        consultant_name = f"Consultant {user_consult_id}"
                else:
                    consultant_name = f"Consultant {user_consult_id}"
                
                # Get IP address
                client_ip = ""
                if request:
                    client_ip = get_client_ip(request)
                
                # Create app logs record according to specifications
                app_logs = TcdAppLogs(
                    action_date=timezone.now(),
                    action_log=action_log,
                    remark=remark,
                    ip_address=client_ip or "",
                    app_member_id=None,  # For consultant actions, this is None
                    user_consult_id=user_consult_id,
                    name=ConsultantFavoriteService._get_consultant_display_name(consultant, user_consult_id),
                    type="APPCONSULTANT"
                )
                
                # Save the log
                app_logs.save()
                logger.info(f"App logs saved successfully: action={action_log}, consultant_id={user_consult_id}, type=APPCONSULTANT")
                
            except Exception as log_error:
                logger.error(f"Error saving app logs: {str(log_error)}")
                # Don't fail the main operation if logging fails
            
            if not update_success:
                return service_error_response(
                    error_code=3002,
                    message="Data not found"
                )
            
            # Determine action taken
            action = "favorited" if new_consult_favorite == "1" else "unfavorited"
            
            return service_success_response(data={
                'id': project_consult.id,
                'app_project_id': app_project_id,
                'user_consult_id': user_consult_id,
                'old_consult_favorite': old_consult_favorite,
                'new_consult_favorite': new_consult_favorite,
                'is_favorite': new_consult_favorite == '1',
                'action': action,
                'updated': True
            })
            
        except Exception as e:
            logger.error(f"Error updating consultant favorite status: {str(e)}")
            
            # Log the failed action
            try:
                # Get consultant information
                from authentication.models import TcdUserConsult
                consultant = TcdUserConsult.objects.filter(id=user_consult_id).first()
                consultant_name = ""
                if consultant:
                    # Try to get the consultant's name from different fields
                    if hasattr(consultant, 'username') and consultant.username:
                        consultant_name = consultant.username
                    elif hasattr(consultant, 'name') and consultant.name:
                        consultant_name = consultant.name
                    elif hasattr(consultant, 'first_name') and consultant.first_name:
                        consultant_name = consultant.first_name
                        if hasattr(consultant, 'last_name') and consultant.last_name:
                            consultant_name += f" {consultant.last_name}"
                    else:
                        consultant_name = f"Consultant {user_consult_id}"
                else:
                    consultant_name = f"Consultant {user_consult_id}"
                
                # Get IP address
                client_ip = ""
                if request:
                    client_ip = get_client_ip(request)
                
                # Try to get old consult favorite status for logging
                try:
                    project_consult = TcdAppProjectConsult.objects.filter(
                        app_project_id=app_project_id,
                        user_consult_id=user_consult_id
                    ).first()
                    old_favorite = project_consult.consult_favorite if project_consult else "0"
                except:
                    old_favorite = "0"  # Default fallback
                
                action_log = "เพิ่มรายการที่ชอบ" if old_favorite == "0" else "ยกเลิกรายการที่ชอบ"
                remark = f"{action_log} ไม่สำเร็จ"
                
                # Create failed app logs record according to specifications
                app_logs = TcdAppLogs(
                    action_date=timezone.now(),
                    action_log=action_log,
                    remark=remark,
                    ip_address=client_ip or "",
                    app_member_id=None,
                    user_consult_id=user_consult_id,
                    name=consultant_name,
                    type="APPCONSULTANT"  # Updated to match requirement
                )
                
                # Save the failed log
                app_logs.save()
                logger.info(f"Failed app logs saved: action={action_log}, consultant_id={user_consult_id}, type=APPCONSULTANT")
                
            except Exception as log_error:
                logger.error(f"Error saving failed app logs: {str(log_error)}")
            
            return service_error_response(
                error_code=5000,
                message="System error"
            )

    